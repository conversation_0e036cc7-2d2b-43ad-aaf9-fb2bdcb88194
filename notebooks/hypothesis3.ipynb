{"cells": [{"cell_type": "markdown", "id": "91e59365", "metadata": {}, "source": ["## Hipótese para problemática 3 — Público Ideal\n", "\n", "**Hipótese de Pesquisa**  \n", "- H₀: A participação de lentes de grau nas vendas **é a mesma** entre estados/cidades.  \n", "- H₁: A participação de lentes de grau **varia** por estado/cidade.  \n", "\n", "**Justificativa**  \n", "Analisar se o impacto das Óticas Chilli Beans é o mesmo entre as diferentes localidades. Dessa forma, verificar se a participação das vendas de lentes de grau difere entre localidades (UF/cidade) para orientar decisões de sortimento, estoque e campanhas regionais.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6db35947", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estatística Qui-quadrado (χ²): 233.5066\n", "P-valor: 2.83e-27\n", "Graus de liberdade: 45\n", "Nível de significância (α): 0.05\n", "\n", "HIPÓTESES:\n", "H₀: A participação de lentes de grau nas vendas é a mesma entre estados\n", "H₁: A participação de lentes de grau varia por estado\n", "\n", "RESULTADO: REJEITAMOS H₀\n", "Conclusão: Rejeitamos a hipótese de que 'a participação é a mesma entre estados'\n", "Com p-valor = 2.83e-27 < 0.05, ACEITAMOS H₁:\n", "A participação de lentes de grau VARIA significativamente entre estados.\n", "Percentual de GRAU por estado (primeiros 10):\n", "Dim_Cliente.Uf_Cliente\n", "AC                       6.15\n", "AL                       3.59\n", "AL                       0.00\n", "AM                      18.53\n", "AM                       0.00\n", "AP                      16.67\n", "AP                      33.33\n", "BA                       4.02\n", "CE                       5.35\n", "DF                       7.04\n", "Name: GRAU, dtype: float64\n", "\n", "Proporção geral de GRAU: 8.92%\n", "Proporção geral de SOLAR: 91.08%\n", "Resíduos padronizados para GRAU (valores > 2 ou < -2 são significativos):\n", "Dim_Cliente.Uf_Cliente\n", "AM    5.69\n", "RJ   -4.97\n", "BA   -4.84\n", "RS    4.65\n", "SP    4.64\n", "EX   -4.03\n", "PE   -3.98\n", "ES   -3.05\n", "AL   -2.67\n", "PI    2.06\n", "Name: GRAU, dtype: float64\n"]}], "source": ["import pandas as pd\n", "from scipy.stats import chi2_contingency\n", "import numpy as np\n", "\n", "\n", "def test_lentes_por_geo(csv_path='../assets/dados.csv', geo_col='Dim_Cliente.Uf_Cliente'):\n", "    \"\"\"<PERSON>ega dados, mapeia Tipo_Item e realiza teste qui-quadrado por coluna geográfica.\n", "\n", "    Parâmetros:\n", "    - csv_path: caminho para o CSV\n", "    - geo_col: coluna geo<PERSON>r<PERSON> a usar (ex.: 'Dim_Cliente.Uf_Cliente' ou 'Dim_Cliente.Cidade_cliente')\n", "\n", "    Retorna:\n", "    - dict com tabela observada, estatísticas e frequências esperadas\n", "    \"\"\"\n", "    df = pd.read_csv(csv_path)\n", "\n", "    # Normalizar e mapear grupo de produto para GRAU/SOLAR\n", "    df['produto_grupo'] = df['Dim_Produtos.Grupo_Produto'].astype(str).str.strip().str.upper()\n", "    df['produto_grupo'] = df['produto_grupo'].str.replace('Ó', 'O')\n", "    df['Tipo_Item'] = pd.NA\n", "    df.loc[df['produto_grupo'].str.contains('LENTES', na=False), 'Tipo_Item'] = 'GRAU'\n", "    df.loc[df['produto_grupo'].str.contains('SOLAR|OCULOS|OCULOS SOLARES|OCULOS DE SOL', na=False) & ~df['produto_grupo'].str.contains('LENTES', na=False), 'Tipo_Item'] = 'SOLAR'\n", "\n", "    # Remover linhas sem classificacao de tipo\n", "    df = df.dropna(subset=['Tipo_Item', geo_col])\n", "\n", "    # Montar tabela de contingência\n", "    tab = pd.crosstab(df[geo_col], df['Tipo_Item'])\n", "\n", "    # Executar teste qui-quadrado\n", "    chi2, p, dof, expected = chi2_contingency(tab)\n", "\n", "    return {\n", "        'observed': tab,\n", "        'chi2': chi2,\n", "        'p_value': p,\n", "        'dof': dof,\n", "        'expected': pd.DataFrame(expected, index=tab.index, columns=tab.columns),\n", "        'df_filtered': df\n", "    }\n", "\n", "\n", "# Exemplo de uso: teste por UF\n", "res_uf = test_lentes_por_geo('../assets/dados.csv', geo_col='Dim_Cliente.Uf_Cliente')\n", "\n", "print(f\"Estatística Qui-quadrado (χ²): {res_uf['chi2']:.4f}\")\n", "print(f\"P-valor: {res_uf['p_value']:.2e}\")\n", "print(f\"Graus de liberdade: {res_uf['dof']}\")\n", "print(f\"Nível de significância (α): 0.05\")\n", "print()\n", "\n", "print(\"HIPÓTESES:\")\n", "print(\"H₀: A participação de lentes de grau nas vendas é a mesma entre estados\")\n", "print(\"H₁: A participação de lentes de grau varia por estado\")\n", "print()\n", "\n", "# Interpretação do teste\n", "if res_uf['p_value'] < 0.05:\n", "    print(\"RESULTADO: REJEITAMOS H₀\")\n", "    print(\"Conclusão: Rejeitamos a hipótese de que 'a participação é a mesma entre estados'\")\n", "    print(f\"Com p-valor = {res_uf['p_value']:.2e} < 0.05, ACEITAMOS H₁:\")\n", "    print(\"A participação de lentes de grau VARIA significativamente entre estados.\")\n", "else:\n", "    print(\"RESULTADO: NÃO REJEITAMOS H₀\")\n", "    print(\"Conclusão: Não temos evidência suficiente para rejeitar H₀\")\n", "    print(f\"Com p-valor = {res_uf['p_value']:.6f} ≥ 0.05, mantemos H₀:\")\n", "    print(\"A participação de lentes de grau NÃO varia significativamente entre estados.\")\n", "\n", "# An<PERSON>lise das proporções por estado\n", "tab_prop = res_uf['observed'].div(res_uf['observed'].sum(axis=1), axis=0) * 100\n", "print(\"Percentual de GRAU por estado (primeiros 10):\")\n", "print(tab_prop['GRAU'].head(10).round(2))\n", "\n", "print(f\"\\nProporção geral de GRAU: {res_uf['observed']['GRAU'].sum() / res_uf['observed'].sum().sum() * 100:.2f}%\")\n", "print(f\"Proporção geral de SOLAR: {res_uf['observed']['SOLAR'].sum() / res_uf['observed'].sum().sum() * 100:.2f}%\")\n", "\n", "# Verificar quais estados mais se desviam do esperado\n", "residuos = (res_uf['observed'] - res_uf['expected']) / np.sqrt(res_uf['expected'])\n", "print(\"Resíduos padronizados para GRAU (valores > 2 ou < -2 são significativos):\")\n", "residuos_grau = residuos['GRAU'].sort_values(key=abs, ascending=False)\n", "print(residuos_grau.head(10).round(2))"]}, {"cell_type": "markdown", "id": "31375584", "metadata": {}, "source": ["**Interpretação dos Resultados:**\n", "\n", "**Resultado do Teste Qui-quadrado:**\n", "- **χ² = 233.51, p = 2.83×10⁻²⁷** (extremamente significativo)\n", "- **Decisão**: Rejeitamos H₀ e aceitamos H₁\n", "- **Conclusão**: A participação de lentes de grau **varia significativamente** entre estados"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}