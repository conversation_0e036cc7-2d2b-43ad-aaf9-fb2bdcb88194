{"cells": [{"cell_type": "markdown", "id": "5f79096b", "metadata": {}, "source": ["# NOTA PARA CORRETOR\n", "#### **Este notebook foi produzido com IA durante a Sprint Review de forma rápida apenas para mostrar alguns dados que o parceiro pediu**"]}, {"cell_type": "code", "execution_count": 1, "id": "607c23e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados iniciais: 40,291 registros\n", "Remoção DEVOLUCAO DE MERCADORIA: 1,281 registros removidos\n", "Preço Varejo > 1: 467 registros removidos\n", "Total Preço Varejo > 1: 0 registros removidos\n", "Validação cálculo valor total: 0 registros removidos\n", "Validação valor total (tolerância 2): 0 registros removidos\n", "Idade entre 10 e 100 anos: 19,255 registros removidos\n", "Idade >= 18 anos no cadastro: 594 registros removidos\n", "Remoção duplicatas: 96 registros removidos\n", "Lojas com Tipo PDV válido: 231 registros removidos\n", "Produtos com nome válido: 0 registros removidos\n", "Produtos com grupo válido: 0 registros removidos\n", "Limpeza de espaços em branco nas colunas categóricas concluída\n", "Regras de negócios aplicadas\n", "\n", "Dados finais: 18,367 registros\n", "Dataset carregado: 18,367 registros\n", "Tipos de PDV únicos: ['LOJA OTICO' 'LOJA' 'LOJA DE RUA HIBRIDO' 'QUIOSQUE HIBRIDO' 'QUIOSQUE'\n", " 'LOJA HIBRIDO' 'ECO CHILLI' 'ECO CHILLI HÍBRIDO' 'LOJA CONCEPT'\n", " 'LOJA DE RUA' 'LOJA DE RUA OTICO' 'QUIOSQUE OUTLET' 'QUIOSQUE OTICO'\n", " 'LOJA OUTLET' 'LOJA DE RUA OUTLET']\n", "\n", "Distribuição das lojas:\n", "Categoria_Loja\n", "Não de Rua    15410\n", "De Rua         2957\n", "Name: count, dtype: int64\n", "\n", "Tipos de PDV por categoria:\n", "De Rua: ['LOJA DE RUA HIBRIDO', 'LOJA DE RUA', 'LOJA DE RUA OTICO', 'LOJA DE RUA OUTLET']\n", "Não de Rua: ['LOJA OTICO', 'LOJA', 'QUIOSQUE HIBRIDO', 'QUIOSQUE', 'LOJA HIBRIDO', 'ECO CHILLI', 'ECO CHILLI HÍBRIDO', 'LOJA CONCEPT', 'QUIOSQUE OUTLET', 'QUIOSQUE OTICO', 'LOJA OUTLET']\n", "\n", "Estatísticas do Volume de Vendas (Quantidade) por Categoria:\n", "                count    sum  mean  median   std  min  max\n", "Categoria_Loja                                            \n", "De Rua           2957   3203  1.08     1.0  0.30    1    4\n", "Não de Rua      15410  16821  1.09     1.0  0.33    1   12\n", "\n", "Teste t-Student para Volume de Vendas:\n", "Estatística t: -1.2792\n", "P-valor: 0.200841\n", "Diferença significativa (α=0.05): Não\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dq/83qxsj710bbgjhs2_rywgz900000gn/T/ipykernel_58031/3958565585.py:63: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.boxplot(data=df, x='Categoria_Loja', y='Quantidade', ax=ax1, palette=['#2E8B57', '#FF6347'])\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAScCAYAAABk5MYMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjUsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvWftoOwAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Qm8jOX7x/HLrlA51qikkiVLi2jzqyztG1FUSipKKK3ayRqiRSkhtGghStpV2rUvorKUFJW17ML5v77373/P7zljnmPO4szMmc/79ZoX88ycmftZ536u57qvp0hmZmamAQAAAAAAAACAHRTdcRIAAAAAAAAAABCC6AAAAAAAAAAAhCCIDgAAAAAAAABACILoAAAAAAAAAACEIIgOAAAAAAAAAEAIgugAAAAAAAAAAIQgiA4AAAAAAAAAQAiC6AAAAAAAAAAAhCCIDgAACoWtW7cmugkAAAAAgEKIIDoApKkNGzbY4YcfbrVr13aP1q1bF8j3Tps2LfKdnTt3ts2bN2d5/YUXXoi83rFjx8j03377LTJdj2T0008/2d13322nnXaaW7YNGza05s2b2/XXX2+zZ89OdPMKtVdeecUuueSSHaYHtxltQ8nqr7/+srp160ba+swzz2S77zZq1Cjy3smTJ+fqO7Vt+s9I1+1zVx9XdCzwn33ddddl+94LL7ww8t6bbrrJkoG2C98mbS/JuM525X6djPO/********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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "ANÁLISE DETALHADA DE VOLUME POR TIPO DE PDV\n", "============================================================\n", "                     count   sum  mean  median\n", "Dim_Lojas.Tipo_PDV                            \n", "LOJA                  6723  7209  1.07     1.0\n", "QUIOSQUE              3455  3624  1.05     1.0\n", "LOJA OTICO            2903  3555  1.22     1.0\n", "LOJA DE RUA HIBRIDO   2498  2678  1.07     1.0\n", "QUIOSQUE HIBRIDO       734   753  1.03     1.0\n", "LOJA HIBRIDO           708   736  1.04     1.0\n", "ECO CHILLI             331   340  1.03     1.0\n", "QUIOSQUE OTICO         297   328  1.10     1.0\n", "LOJA DE RUA            259   272  1.05     1.0\n", "LOJA DE RUA OTICO      192   244  1.27     1.0\n", "LOJA CONCEPT           111   126  1.14     1.0\n", "ECO CHILLI HÍBRIDO      84    85  1.01     1.0\n", "LOJA OUTLET             40    41  1.02     1.0\n", "QUIOSQUE OUTLET         24    24  1.00     1.0\n", "LOJA DE RUA OUTLET       8     9  1.12     1.0\n", "\n", "TOP 5 TIPOS DE PDV - MAIOR VOLUME TOTAL:\n", "1º. LOJA: 7,209 unidades (Não de Rua)\n", "2º. QUIOSQUE: 3,624 unidades (Não de Rua)\n", "3º. LOJA OTICO: 3,555 unidades (Não de Rua)\n", "4º. LOJA DE RUA HIBRIDO: 2,678 unidades (De Rua)\n", "5º. QUIOSQUE HIBRIDO: 753 unidades (Não de Rua)\n", "\n", "TOP 5 TIPOS DE PDV - MENOR VOLUME TOTAL:\n", "1º. LOJA CONCEPT: 126 unidades (Não de Rua)\n", "2º. ECO CHILLI HÍBRIDO: 85 unidades (Não de Rua)\n", "3º. LOJA OUTLET: 41 unidades (Não de Rua)\n", "4º. QUIOSQUE OUTLET: 24 unidades (Não de Rua)\n", "5º. LOJA DE RUA OUTLET: 9 unidades (De Rua)\n", "\n", "============================================================\n", "RESUMO EXECUTIVO - VOLUME DE VENDAS\n", "============================================================\n", "• Volume Total Lojas de Rua: 3,203 unidades\n", "• Volume Total Lojas Não de Rua: 16,821 unidades\n", "• Diferença Total Absoluta: -13,618 unidades\n", "• Diferença Total Percentual: -81.0%\n", "\n", "• Volume Médio por Transação - Lojas de Rua: 1.08 unidades\n", "• Volume Médio por Transação - Lojas Não de Rua: 1.09 unidades\n", "• Diferença Média Absoluta: -0.01 unidades\n", "• Diferença Média Percentual: -0.8%\n", "• Significância Estatística: Não (p=0.2008)\n", "• Conclusão: Lojas de rua têm volume total 81.0% MENOR\n", "• Conclusão: Lojas de rua têm volume médio por transação 0.8% MENOR\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "from scipy import stats\n", "import sys\n", "sys.path.append('..')\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados\n", "df = apply_business_filters('../assets/dados.csv', verbose=True)\n", "\n", "print(f\"Dataset carregado: {len(df):,} registros\")\n", "print(f\"Tipos de PDV únicos: {df['Dim_Lojas.Tipo_PDV'].unique()}\")\n", "\n", "# Classificar lojas como \"<PERSON> Rua\" ou \"Não de Rua\" baseado no nome do Tipo_PDV\n", "def classificar_loja(tipo_pdv):\n", "    if pd.isna(tipo_pdv):\n", "        return '<PERSON><PERSON>'\n", "    return '<PERSON>' if 'RUA' in str(tipo_pdv).upper() else '<PERSON><PERSON> de Rua'\n", "\n", "df['Categoria_Loja'] = df['Dim_Lojas.Tipo_PDV'].apply(classificar_loja)\n", "\n", "print(f\"\\nDistribuição das lojas:\")\n", "print(df['Categoria_Loja'].value_counts())\n", "\n", "print(f\"\\nTipos de PDV por categoria:\")\n", "for categoria in ['<PERSON> Rua', '<PERSON><PERSON> de Rua']:\n", "    tipos = df[df['Categoria_Loja'] == categoria]['Dim_Lojas.Tipo_PDV'].unique()\n", "    print(f\"{categoria}: {list(tipos)}\")\n", "\n", "# Análise estatística descritiva do VOLUME DE VENDAS (Quantidade)\n", "volume_stats = df.groupby('Categoria_Loja')['Quantidade'].agg([\n", "    'count', 'sum', 'mean', 'median', 'std', 'min', 'max'\n", "]).round(2)\n", "\n", "print(f\"\\nEstatísticas do Volume de Vendas (Quantidade) por Categoria:\")\n", "print(volume_stats)\n", "\n", "# Teste estatístico (t-test) para quantidade\n", "rua_volume = df[df['Categoria_Loja'] == 'De Rua']['Quantidade']\n", "nao_rua_volume = df[df['Categoria_Loja'] == 'Não de Rua']['Quantidade']\n", "\n", "t_stat, p_value = stats.ttest_ind(rua_volume, nao_rua_volume)\n", "\n", "print(f\"\\nTeste t-Student para Volume de Vendas:\")\n", "print(f\"Estatística t: {t_stat:.4f}\")\n", "print(f\"P-valor: {p_value:.6f}\")\n", "print(f\"Diferença significativa (α=0.05): {'Sim' if p_value < 0.05 else 'Não'}\")\n", "\n", "# Configuração dos gráficos\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "plt.rcParams['figure.facecolor'] = 'white'\n", "plt.rcParams['font.size'] = 10\n", "\n", "# Criar figura com subplots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Análise Comparativa: Volume de Vendas Lojas de Rua vs Não de Rua', \n", "             fontsize=16, fontweight='bold')\n", "\n", "# 1. Boxplot comparativo\n", "ax1 = axes[0, 0]\n", "sns.boxplot(data=df, x='Categoria_Loja', y='Quantidade', ax=ax1, palette=['#2E8B57', '#FF6347'])\n", "ax1.set_title('Distribuição do Volume de Vendas por Categoria')\n", "ax1.set_xlabel('Categoria da Loja')\n", "ax1.set_ylabel('Quantidade Vendida')\n", "\n", "# 2. Histograma comparativo\n", "ax2 = axes[0, 1]\n", "for i, categoria in enumerate(['<PERSON>', '<PERSON><PERSON> de Rua']):\n", "    data = df[df['Categoria_Loja'] == categoria]['Quantidade']\n", "    ax2.hist(data, alpha=0.7, bins=30, label=categoria, \n", "             color=['#2E8B57', '#FF6347'][i], density=True)\n", "ax2.set_title('Distribuição de Frequência do Volume de Vendas')\n", "ax2.set_xlabel('Quantidade Vendida')\n", "ax2.set_ylabel('Densidade')\n", "ax2.legend()\n", "\n", "# 3. Gráfico de barras com volume total\n", "ax3 = axes[1, 0]\n", "volume_total = df.groupby('Categoria_Loja')['Quantidade'].sum()\n", "bars = ax3.bar(volume_total.index, volume_total.values, color=['#2E8B57', '#FF6347'], alpha=0.8)\n", "ax3.set_title('Volume Total de Vendas por Categoria')\n", "ax3.set_xlabel('Categoria da Loja')\n", "ax3.set_ylabel('Quantidade Total Vendida')\n", "\n", "# Adicionar valores nas barras\n", "for bar, valor in zip(bars, volume_total.values):\n", "    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + valor*0.01,\n", "             f'{valor:,.0f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 4. <PERSON><PERSON><PERSON><PERSON> por região\n", "ax4 = axes[1, 1]\n", "volume_regiao = df.groupby(['Dim_Lojas.REGIAO_CHILLI', 'Categoria_Loja'])['Quantidade'].sum().unstack()\n", "volume_regiao.plot(kind='bar', ax=ax4, color=['#2E8B57', '#FF6347'], alpha=0.8)\n", "ax4.set_title('Volume Total de Vendas por Região e Categoria')\n", "ax4.set_xlabel('Região')\n", "ax4.set_ylabel('Quantidade Total Vendida')\n", "ax4.legend(title='Categoria')\n", "ax4.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> detalhada por tipo de PDV\n", "print(f\"\\n\" + \"=\"*60)\n", "print(\"ANÁLISE DETALHADA DE VOLUME POR TIPO DE PDV\")\n", "print(\"=\"*60)\n", "\n", "volume_por_tipo = df.groupby('Dim_Lojas.Tipo_PDV')['Quantidade'].agg([\n", "    'count', 'sum', 'mean', 'median'\n", "]).round(2).sort_values('sum', ascending=False)\n", "\n", "print(volume_por_tipo)\n", "\n", "# Top 5 tipos de PDV por volume total\n", "print(f\"\\nTOP 5 TIPOS DE PDV - MAIOR VOLUME TOTAL:\")\n", "for i, (tipo, valor) in enumerate(volume_por_tipo['sum'].head(5).items(), 1):\n", "    categoria = classificar_loja(tipo)\n", "    print(f\"{i}º. {tipo}: {valor:,.0f} unidades ({categoria})\")\n", "\n", "# Bottom 5 tipos de PDV por volume total\n", "print(f\"\\nTOP 5 TIPOS DE PDV - MENOR VOLUME TOTAL:\")\n", "for i, (tipo, valor) in enumerate(volume_por_tipo['sum'].tail(5).items(), 1):\n", "    categoria = classificar_loja(tipo)\n", "    print(f\"{i}º. {tipo}: {valor:,.0f} unidades ({categoria})\")\n", "\n", "# Resumo executivo\n", "print(f\"\\n\" + \"=\"*60)\n", "print(\"RESUMO EXECUTIVO - VOLUME DE VENDAS\")\n", "print(\"=\"*60)\n", "\n", "volume_total_rua = rua_volume.sum()\n", "volume_total_nao_rua = nao_rua_volume.sum()\n", "volume_medio_rua = rua_volume.mean()\n", "volume_medio_nao_rua = nao_rua_volume.mean()\n", "\n", "diferenca_absoluta_total = volume_total_rua - volume_total_nao_rua\n", "diferenca_percentual_total = (diferenca_absoluta_total / volume_total_nao_rua) * 100\n", "\n", "diferenca_absoluta_media = volume_medio_rua - volume_medio_nao_rua\n", "diferenca_percentual_media = (diferenca_absoluta_media / volume_medio_nao_rua) * 100\n", "\n", "print(f\"• Volume Total Lojas de Rua: {volume_total_rua:,.0f} unidades\")\n", "print(f\"• Volume Total Lojas Não de Rua: {volume_total_nao_rua:,.0f} unidades\")\n", "print(f\"• Diferença Total Absoluta: {diferenca_absoluta_total:,.0f} unidades\")\n", "print(f\"• Diferença Total Percentual: {diferenca_percentual_total:+.1f}%\")\n", "\n", "print(f\"\\n• Volume Médio por Transação - Lojas de Rua: {volume_medio_rua:.2f} unidades\")\n", "print(f\"• Volume Médio por Transação - Lojas Não de Rua: {volume_medio_nao_rua:.2f} unidades\")\n", "print(f\"• Diferença Média Absoluta: {diferenca_absoluta_media:.2f} unidades\")\n", "print(f\"• Diferença Média Percentual: {diferenca_percentual_media:+.1f}%\")\n", "\n", "print(f\"• Significância Estatística: {'Sim' if p_value < 0.05 else 'Não'} (p={p_value:.4f})\")\n", "\n", "if diferenca_absoluta_total > 0:\n", "    print(f\"• Conclusão: Lojas de rua têm volume total {diferenca_percentual_total:.1f}% MAIOR\")\n", "else:\n", "    print(f\"• Conclusão: Lojas de rua têm volume total {abs(diferenca_percentual_total):.1f}% MENOR\")\n", "\n", "if diferenca_absoluta_media > 0:\n", "    print(f\"• Conclusão: Lojas de rua têm volume médio por transação {diferenca_percentual_media:.1f}% MAIOR\")\n", "else:\n", "    print(f\"• Conclusão: Lojas de rua têm volume médio por transação {abs(diferenca_percentual_media):.1f}% MENOR\")"]}, {"cell_type": "markdown", "id": "33e0f3f9", "metadata": {}, "source": ["# Interpretação dos Resultados - Volume de Vendas\n", "\n", "## Princi<PERSON><PERSON> Des<PERSON>\n", "\n", "### 1. **Distribuição das Lojas**\n", "- **Lojas Não de Rua**: 15.410 transações (83,9%)\n", "- **Lojas de Rua**: 2.957 transações (16,1%)\n", "\n", "### 2. **Volume Total de Vendas**\n", "- **Lojas Não de Rua**: 16.821 unidades vendidas\n", "- **Lojas de Rua**: 3.203 unidades vendidas\n", "- **Diferença**: Lojas não de rua vendem 425% mais em volume total\n", "\n", "### 3. **Volume Médio por Transação**\n", "- **Lojas Não de Rua**: 1,09 unidades por transação\n", "- **Lojas de Rua**: 1,08 unidades por transação\n", "- **Diferença**: Praticamente idêntica (diferença de apenas 0,9%)\n", "\n", "### 4. **<PERSON><PERSON>lise Estatística**\n", "- **Teste t-Student**: p-valor = 0,20 (não significativo)\n", "- **Conclusão**: Não há diferença estatisticamente significativa no volume médio por transação"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}