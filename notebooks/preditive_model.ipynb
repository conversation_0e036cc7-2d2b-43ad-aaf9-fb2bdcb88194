# Importações fundamentais para análise de dados
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from typing import Dict, List, Tuple, Optional

# Configuração de visualizações
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Importação dos módulos de pré-processamento desenvolvidos
# Estes módulos encapsulam as regras de negócio e transformações validadas
import sys
sys.path.append('..')
from data_filtering import apply_business_filters
from data_preprocessing import preprocess_data

# Importações do Scikit-learn para Machine Learning
# Seguindo as melhores práticas de Pipeline e modularização

# === APRENDIZADO NÃO SUPERVISIONADO (Clustering) ===
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.mixture import GaussianMixture

# === APRENDIZADO SUPERVISIONADO (Predição) ===
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.tree import DecisionTreeRegressor

# === PRÉ-PROCESSAMENTO E PIPELINE ===
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer

# === AVALIAÇÃO E VALIDAÇÃO ===
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import (
    silhouette_score, calinski_harabasz_score, davies_bouldin_score,  # Clustering
    mean_squared_error, mean_absolute_error, r2_score  # Regressão
)

# === REDUÇÃO DE DIMENSIONALIDADE ===
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

# Carregamento dos dados com aplicação das regras de negócio
# O módulo data_filtering aplica filtros validados para garantir qualidade dos dados

print("📥 Carregando dados da Chilli Beans...")
df_raw = apply_business_filters('../assets/dados.csv', verbose=True)
df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']
df_raw = df_raw[df_raw['Dim_Lojas.Cidade_Emp'] != 'SAO PAULO']
df_raw = df_raw[df_raw['Dim_Lojas.Cidade_Emp'] != 'SÃO PAULO']
df_raw = df_raw[df_raw['Dim_Lojas.Cidade_Emp'] != 'Sao Paulo']
df_raw = df_raw[df_raw['Dim_Lojas.Cidade_Emp'] != 'São Paulo']

print(f"\n📊 Dataset carregado: {df_raw.shape[0]:,} registros, {df_raw.shape[1]} colunas")
print(f"📅 Período dos dados: {df_raw['ID_Date'].min()} a {df_raw['ID_Date'].max()}")

# Visualizar estrutura básica dos dados
print("\n🔍 Primeiras linhas do dataset:")
display(df_raw.head())

print("\n📋 Informações sobre as colunas:")
print(df_raw.info())

from difflib import SequenceMatcher
import unicodedata

# Corrigir nomes de cidades semelhantes via fuzzy matching e padronizar para CAPS

def strip_accents(text: str) -> str:
    if not isinstance(text, str):
        text = str(text)
    return ''.join(
        ch for ch in unicodedata.normalize('NFKD', text)
        if not unicodedata.combining(ch)
    )

# Coluna alvo
col_city = 'Dim_Lojas.Cidade_Emp'
assert col_city in df_raw.columns, f"Coluna '{col_city}' não encontrada em df_raw"

# Valores originais
cities_series = df_raw[col_city].astype(str).str.strip()
cities_upper = cities_series.str.upper()

# Frequência de cada cidade (após upper)
freq = cities_upper.value_counts()
unique_cities = list(freq.index)

# Agrupamento fuzzy em cima das cidades sem acentos
threshold = 0.96  # limiar alto para evitar junções incorretas
groups = []       # lista de grupos, cada grupo é um set de nomes (em CAPS)
group_keys = []   # chave "sem acento" representativa de cada grupo

for name in unique_cities:
    key = strip_accents(name)
    placed = False
    # Tenta encontrar um grupo existente com chave similar
    for i, gkey in enumerate(group_keys):
        sim = SequenceMatcher(None, key, gkey).ratio()
        if sim >= threshold:
            groups[i].add(name)
            placed = True
            break
    if not placed:
        groups.append({name})
        group_keys.append(key)

# Escolher canônico por grupo: mais frequente; em caso de empate, preferir com acento
canonical_per_group = []
for members in groups:
    members = list(members)
    # Ordena por frequência (desc), depois preferir com acento
    members_sorted = sorted(
        members,
        key=lambda n: (freq.get(n, 0), any(ord(c) > 127 for c in n)),
        reverse=True
    )
    canonical_per_group.append(members_sorted[0])

# Construir dicionário de mapeamento cidade -> canônico (todos em CAPS)
mapping = {}
for members, canonical in zip(groups, canonical_per_group):
    for m in members:
        mapping[m] = canonical

# Aplicar mapeamento
before_unique = cities_upper.nunique()
df_raw[col_city] = cities_upper.map(mapping).fillna(cities_upper)
after_unique = df_raw[col_city].nunique()

# Relatório breve
corrected_count = sum(1 for k, v in mapping.items() if k != v)
print(f"Padronização de cidades concluída.")
print(f"• Cidades únicas antes: {before_unique}")
print(f"• Cidades únicas depois: {after_unique}")
print(f"• Variações unificadas (fuzzy): {corrected_count}")

# Exibir algumas correções aplicadas
sample_changes = [(k, v) for k, v in mapping.items() if k != v][:10]
if sample_changes:
    print("\nExemplos de correções:")
    for old, new in sample_changes:
        print(f"  - {old} -> {new}")

# Filtro específico para óculos de grau (lentes)
# Este filtro é crucial para responder à pergunta de negócio sobre óticas especializadas

print("👓 Filtrando dados específicos de óculos de grau...")

df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']

# Identificar produtos relacionados a óculos de grau
# Baseado na análise dos grupos de produtos disponíveis
df_raw['produto_grupo_clean'] = df_raw['Dim_Produtos.Grupo_Produto'].astype(str).str.strip().str.upper()

# Criar flag para óculos de grau (lentes)
df_raw['is_prescription_glasses'] = df_raw['produto_grupo_clean'].str.contains('LENTES', na=False)

# Estatísticas sobre óculos de grau vs outros produtos
prescription_stats = df_raw.groupby('is_prescription_glasses').agg({
    'Quantidade': ['count', 'sum'],
    'Valor_Total': ['sum', 'mean'],
    'ID_Cliente': 'nunique'
}).round(2)

print("\n📊 Distribuição: Óculos de Grau vs Outros Produtos")
display(prescription_stats)

# Criar dataset específico para óculos de grau
df_prescription = df_raw[df_raw['is_prescription_glasses'] == True].copy()

print(f"\n👓 Dataset de óculos de grau: {df_prescription.shape[0]:,} registros")
print(f"🏪 Lojas com vendas de óculos de grau: {df_prescription['ID_Loja'].nunique()}")
print(f"👥 Clientes únicos: {df_prescription['ID_Cliente'].nunique()}")
print(f"🌍 Cidades atendidas: {df_prescription['Dim_Lojas.Cidade_Emp'].nunique()}")

# Engenharia de Features para Clustering
# Criação de variáveis agregadas por cidade para análise de mercado

print("🔧 Realizando engenharia de features para clustering...")

# Agregar dados por cidade para análise de mercado
# Cada linha representará uma cidade com suas características de mercado
city_features = df_prescription.groupby(['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp']).agg({
    # Métricas de volume e performance
    'Quantidade': ['sum', 'mean', 'count'],  # Volume total, médio e número de transações
    'Valor_Total': ['sum', 'mean', 'std'],   # Receita total, ticket médio e variabilidade
    'Preco_Varejo': ['mean', 'std'],         # Preço médio e variabilidade de preços
    
    # Métricas de clientes
    'ID_Cliente': 'nunique',                 # Número de clientes únicos
    'ID_Loja': 'nunique',                    # Número de lojas na cidade
    
    # Características demográficas (moda para variáveis categóricas)
    'Dim_Cliente.Sexo': lambda x: x.mode().iloc[0] if not x.mode().empty else 'M',
    'Dim_Lojas.REGIAO_CHILLI': lambda x: x.mode().iloc[0] if not x.mode().empty else 'SUDESTE'
}).round(2)

# Flatten column names (remover multi-index)
city_features.columns = ['_'.join(col).strip() if col[1] else col[0] for col in city_features.columns]
city_features = city_features.reset_index()

# Renomear colunas para melhor legibilidade
column_mapping = {
    'Quantidade_sum': 'volume_total',
    'Quantidade_mean': 'volume_medio_transacao',
    'Quantidade_count': 'num_transacoes',
    'Valor_Total_sum': 'receita_total',
    'Valor_Total_mean': 'ticket_medio',
    'Valor_Total_std': 'variabilidade_ticket',
    'Preco_Varejo_mean': 'preco_medio',
    'Preco_Varejo_std': 'variabilidade_preco',
    'ID_Cliente_nunique': 'num_clientes_unicos',
    'ID_Loja_nunique': 'num_lojas',
    'Dim_Cliente.Sexo_<lambda>': 'sexo_predominante',
    'Dim_Lojas.REGIAO_CHILLI_<lambda>': 'regiao'
}

city_features = city_features.rename(columns=column_mapping)

# Tratar valores NaN resultantes de std em cidades com apenas 1 transação
city_features['variabilidade_ticket'] = city_features['variabilidade_ticket'].fillna(0)
city_features['variabilidade_preco'] = city_features['variabilidade_preco'].fillna(0)

print(f"\n📊 Features criadas para {len(city_features)} cidades")
print("\n🔍 Primeiras linhas das features por cidade:")
display(city_features.head())

print("\n📈 Estatísticas descritivas das features numéricas:")
numeric_cols = city_features.select_dtypes(include=[np.number]).columns
display(city_features[numeric_cols].describe())

# Preparação dos dados para clustering usando Pipeline do scikit-learn
# Aplicação das melhores práticas de pré-processamento

print("⚙️ Preparando dados para clustering com Pipeline do scikit-learn...")

# Selecionar features numéricas para clustering
# Excluindo identificadores e variáveis categóricas que serão tratadas separadamente
clustering_features = [
    'volume_total', 'volume_medio_transacao', 'num_transacoes',
    'receita_total', 'ticket_medio', 'variabilidade_ticket',
    'preco_medio', 'variabilidade_preco', 'num_clientes_unicos', 'num_lojas'
]

# Criar Pipeline de pré-processamento para clustering
# Pipeline garante aplicação consistente das transformações
clustering_pipeline = Pipeline([
    # Etapa 1: Imputação de valores ausentes com mediana (robusto a outliers)
    ('imputer', SimpleImputer(strategy='median')),
    
    # Etapa 2: Padronização Z-Score para equalizar escalas das variáveis
    # Essencial para algoritmos baseados em distância como K-Means
    ('scaler', StandardScaler())
])

# Aplicar pipeline de pré-processamento
X_clustering = clustering_pipeline.fit_transform(city_features[clustering_features])

print(f"✅ Dados preparados para clustering: {X_clustering.shape}")
print(f"📊 Features utilizadas: {clustering_features}")

# Verificar qualidade da padronização
print(f"\n🔍 Verificação da padronização:")
print(f"Média das features padronizadas: {np.mean(X_clustering, axis=0).round(3)}")
print(f"Desvio padrão das features padronizadas: {np.std(X_clustering, axis=0).round(3)}")

# Determinação do número ótimo de clusters
# Utilizando método do cotovelo e silhouette score

print("🔍 Determinando número ótimo de clusters...")

# Testar diferentes números de clusters
k_range = range(2, 11) # Ignoramos o 1 e 2 porque não faz sentido
inertias = []
silhouette_scores = []

for k in k_range:
    # Aplicar K-Means com diferentes valores de k
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(X_clustering)
    
    # Calcular métricas de avaliação
    inertias.append(kmeans.inertia_)  # WCSS (Within-Cluster Sum of Squares)
    silhouette_scores.append(silhouette_score(X_clustering, cluster_labels))
    
# Visualizar métricas para determinação do k ótimo
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Gráfico do método do cotovelo
ax1.plot(k_range, inertias, 'bo-', linewidth=2, markersize=8)
ax1.set_xlabel('Número de Clusters (k)')
ax1.set_ylabel('Inércia (WCSS)')
ax1.set_title('Método do Cotovelo para Determinação do K Ótimo')
ax1.grid(True, alpha=0.3)

# Gráfico do silhouette score
ax2.plot(k_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)
ax2.set_xlabel('Número de Clusters (k)')
ax2.set_ylabel('Silhouette Score')
ax2.set_title('Silhouette Score por Número de Clusters')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Encontrar k ótimo baseado no maior silhouette score
optimal_k = 3
best_silhouette = max(silhouette_scores)

print(f"\n🎯 Número ótimo de clusters: {optimal_k}")
print(f"📊 Melhor Silhouette Score: {best_silhouette:.3f}")

# Mostrar tabela com todas as métricas
metrics_df = pd.DataFrame({
    'K': k_range,
    'Inércia': inertias,
    'Silhouette Score': silhouette_scores
})
print("\n📋 Métricas por número de clusters:")
display(metrics_df)

# Aplicação do clustering final com k ótimo
# Análise detalhada dos segmentos de mercado identificados

print(f"🎯 Aplicando K-Means com {optimal_k} clusters...")

# Aplicar K-Means final com k ótimo
final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
cluster_labels = final_kmeans.fit_predict(X_clustering)

# Adicionar labels dos clusters ao dataset original
city_features['cluster'] = cluster_labels

# Calcular métricas finais de qualidade do clustering
final_silhouette = silhouette_score(X_clustering, cluster_labels)
final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)
final_davies = davies_bouldin_score(X_clustering, cluster_labels)

print(f"\n📊 Métricas de qualidade do clustering final:")
print(f"   • Silhouette Score: {final_silhouette:.3f} (quanto maior, melhor)")
print(f"   • Calinski-Harabasz Score: {final_calinski:.3f} (quanto maior, melhor)")
print(f"   • Davies-Bouldin Score: {final_davies:.3f} (quanto menor, melhor)")

# Análise dos clusters identificados
print(f"\n🔍 Análise dos {optimal_k} segmentos de mercado identificados:")

cluster_analysis = city_features.groupby('cluster').agg({
    'Dim_Lojas.Cidade_Emp': 'count',  # Número de cidades no cluster
    'volume_total': ['mean', 'std'],
    'receita_total': ['mean', 'std'],
    'ticket_medio': ['mean', 'std'],
    'num_clientes_unicos': ['mean', 'std'],
    'num_lojas': ['mean', 'std'],
    'regiao': lambda x: x.mode().iloc[0] if not x.mode().empty else 'Mista'
}).round(2)

# Flatten column names
cluster_analysis.columns = ['_'.join(col).strip() if col[1] else col[0] for col in cluster_analysis.columns]
cluster_analysis = cluster_analysis.rename(columns={
    'Dim_Lojas.Cidade_Emp_count': 'num_cidades',
    'regiao_<lambda>': 'regiao_predominante'
})

print("\n📋 Características dos clusters:")
display(cluster_analysis)

# Identificar cidades em cada cluster
print("\n🏙️ Exemplos de cidades por cluster:")
for cluster_id in sorted(city_features['cluster'].unique()):
    cluster_cities = city_features[city_features['cluster'] == cluster_id]
    top_cities = cluster_cities.nlargest(5, 'receita_total')[['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'receita_total']]
    print(f"\nCluster {cluster_id} (Top 5 cidades por receita):")
    for _, city in top_cities.iterrows():
        print(f"   • {city['Dim_Lojas.Cidade_Emp']}/{city['Dim_Lojas.Estado_Emp']} - R$ {city['receita_total']:,.2f}")

# Definição da variável target para predição de performance
# Criação de métricas de performance baseadas no negócio

print("🎯 Definindo variável target para predição de performance...")

# Criar variável target: Performance Score
# Combina múltiplas métricas de negócio em um score único
# Normalização Min-Max para cada componente (0-1) e depois média ponderada

from sklearn.preprocessing import MinMaxScaler

# Componentes do Performance Score
performance_components = {
    'receita_total': 0.2,      # 40% - Receita é fundamental
    'num_clientes_unicos': 0.2, # 30% - Base de clientes
    'ticket_medio': 0.2,       # 20% - Valor por transação
    'volume_total': 0.4        # 10% - Volume de vendas
}

# Normalizar cada componente (0-1)
scaler_target = MinMaxScaler()
components_normalized = pd.DataFrame()

for component, weight in performance_components.items():
    # Normalizar componente
    normalized_values = scaler_target.fit_transform(city_features[[component]])
    components_normalized[f'{component}_norm'] = normalized_values.flatten()
    
    print(f"📊 {component}: peso {weight*100}% - range [{city_features[component].min():.2f}, {city_features[component].max():.2f}]")

# Calcular Performance Score como média ponderada
city_features['performance_score'] = (
    components_normalized['receita_total_norm'] * 0.4 +
    components_normalized['num_clientes_unicos_norm'] * 0.3 +
    components_normalized['ticket_medio_norm'] * 0.2 +
    components_normalized['volume_total_norm'] * 0.1
)

print(f"\n✅ Performance Score criado: range [{city_features['performance_score'].min():.3f}, {city_features['performance_score'].max():.3f}]")
print(f"📊 Média: {city_features['performance_score'].mean():.3f}, Desvio: {city_features['performance_score'].std():.3f}")

# Visualizar distribuição do Performance Score
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.hist(city_features['performance_score'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
plt.xlabel('Performance Score')
plt.ylabel('Frequência')
plt.title('Distribuição do Performance Score')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.boxplot(city_features['performance_score'])
plt.ylabel('Performance Score')
plt.title('Box Plot do Performance Score')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Top 10 cidades com melhor performance
top_performers = city_features.nlargest(10, 'performance_score')[[
    'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'performance_score', 
    'cluster', 'receita_total', 'num_clientes_unicos'
]]

print("\n🏆 Top 10 cidades com melhor Performance Score:")
display(top_performers)

# Preparação das features para aprendizado supervisionado
# Combinando features originais com informações dos clusters (modelo híbrido)

print("🔧 Preparando features para aprendizado supervisionado...")

#  'variabilidade_ticket', 'variabilidade_preco', 'preco_medio'
# Features numéricas originais
numerical_features = [
    'volume_total', 'volume_medio_transacao', 'num_transacoes',
    'ticket_medio', 'num_clientes_unicos', 'num_lojas'
]

# Features categóricas
categorical_features = ['sexo_predominante', 'regiao']

# Feature híbrida: cluster (resultado do aprendizado não supervisionado)
hybrid_features = ['cluster']

# Criar dataset para modelagem supervisionada
X_features = city_features[numerical_features + categorical_features + hybrid_features].copy()
y_target = city_features['performance_score'].copy()

print(f"📊 Features para modelagem: {X_features.shape[1]} variáveis")
print(f"   • Numéricas: {len(numerical_features)}")
print(f"   • Categóricas: {len(categorical_features)}")
print(f"   • Híbridas (clusters): {len(hybrid_features)}")
print(f"🎯 Target: performance_score ({len(y_target)} observações)")

# Codificar variáveis categóricas usando LabelEncoder
label_encoders = {}
X_encoded = X_features.copy()

for col in categorical_features:
    le = LabelEncoder()
    X_encoded[col] = le.fit_transform(X_features[col].astype(str))
    label_encoders[col] = le
    print(f"🔤 {col}: {len(le.classes_)} categorias únicas")

print("\n✅ Features preparadas para modelagem supervisionada")
display(X_encoded.head())

# Treinamento e avaliação de modelos de regressão
# Comparação de diferentes algoritmos para predição de performance

print("🤖 Treinando modelos de regressão para predição de performance...")

# Divisão treino/teste estratificada
X_train, X_test, y_train, y_test = train_test_split(
    X_encoded, y_target, test_size=0.2, random_state=42
)

print(f"📊 Divisão dos dados:")
print(f"   • Treino: {X_train.shape[0]} observações")
print(f"   • Teste: {X_test.shape[0]} observações")

# Definir modelos para comparação
models = {
    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
    'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
    'Linear Regression': LinearRegression(),
    'Ridge Regression': Ridge(alpha=1.0),
    'Decision Tree': DecisionTreeRegressor(random_state=42)
}

# Pipeline de pré-processamento para modelos supervisionados
preprocessing_pipeline = Pipeline([
    ('imputer', SimpleImputer(strategy='median')),
    ('scaler', StandardScaler())
])

# Treinar e avaliar cada modelo
model_results = {}

for name, model in models.items():
    print(f"\n🔄 Treinando {name}...")
    
    # Criar pipeline completo (pré-processamento + modelo)
    full_pipeline = Pipeline([
        ('preprocessing', preprocessing_pipeline),
        ('model', model)
    ])
    
    # Treinar modelo
    full_pipeline.fit(X_train, y_train)
    
    # Predições
    y_pred_train = full_pipeline.predict(X_train)
    y_pred_test = full_pipeline.predict(X_test)
    
    # Métricas de avaliação
    train_r2 = r2_score(y_train, y_pred_train)
    test_r2 = r2_score(y_test, y_pred_test)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
    test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
    train_mae = mean_absolute_error(y_train, y_pred_train)
    test_mae = mean_absolute_error(y_test, y_pred_test)
    
    # Cross-validation
    cv_scores = cross_val_score(full_pipeline, X_train, y_train, cv=5, scoring='r2')
    
    # Armazenar resultados
    model_results[name] = {
        'pipeline': full_pipeline,
        'train_r2': train_r2,
        'test_r2': test_r2,
        'train_rmse': train_rmse,
        'test_rmse': test_rmse,
        'train_mae': train_mae,
        'test_mae': test_mae,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred_test
    }
    
    print(f"   ✅ R² Teste: {test_r2:.3f} | RMSE: {test_rmse:.3f} | CV: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

print("\n🏆 Treinamento concluído!")

# Comparação detalhada dos modelos
# Análise de performance e seleção do melhor modelo

print("📊 Comparação detalhada dos modelos:")

# Criar DataFrame com resultados
comparison_df = pd.DataFrame({
    'Modelo': list(model_results.keys()),
    'R² Treino': [results['train_r2'] for results in model_results.values()],
    'R² Teste': [results['test_r2'] for results in model_results.values()],
    'RMSE Treino': [results['train_rmse'] for results in model_results.values()],
    'RMSE Teste': [results['test_rmse'] for results in model_results.values()],
    'MAE Teste': [results['test_mae'] for results in model_results.values()],
    'CV Média': [results['cv_mean'] for results in model_results.values()],
    'CV Desvio': [results['cv_std'] for results in model_results.values()]
}).round(4)

# Calcular overfitting (diferença entre R² treino e teste)
comparison_df['Overfitting'] = (comparison_df['R² Treino'] - comparison_df['R² Teste']).round(4)

# Ordenar por R² teste (melhor performance)
comparison_df = comparison_df.sort_values('R² Teste', ascending=False)

print("\n🏆 Ranking dos modelos por performance:")
display(comparison_df)

# Identificar melhor modelo
best_model_name = comparison_df.iloc[0]['Modelo']
best_model = model_results[best_model_name]

print(f"\n🥇 Melhor modelo: {best_model_name}")
print(f"   • R² Teste: {best_model['test_r2']:.4f}")
print(f"   • RMSE Teste: {best_model['test_rmse']:.4f}")
print(f"   • Cross-Validation: {best_model['cv_mean']:.4f} ± {best_model['cv_std']:.4f}")

# Visualizar performance dos modelos
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# R² Score
axes[0,0].bar(comparison_df['Modelo'], comparison_df['R² Teste'], color='skyblue', alpha=0.7)
axes[0,0].set_title('R² Score no Conjunto de Teste')
axes[0,0].set_ylabel('R² Score')
axes[0,0].tick_params(axis='x', rotation=45)
axes[0,0].grid(True, alpha=0.3)

# RMSE
axes[0,1].bar(comparison_df['Modelo'], comparison_df['RMSE Teste'], color='lightcoral', alpha=0.7)
axes[0,1].set_title('RMSE no Conjunto de Teste')
axes[0,1].set_ylabel('RMSE')
axes[0,1].tick_params(axis='x', rotation=45)
axes[0,1].grid(True, alpha=0.3)

# Cross-Validation
axes[1,0].bar(comparison_df['Modelo'], comparison_df['CV Média'], 
              yerr=comparison_df['CV Desvio'], color='lightgreen', alpha=0.7, capsize=5)
axes[1,0].set_title('Cross-Validation Score')
axes[1,0].set_ylabel('CV Score')
axes[1,0].tick_params(axis='x', rotation=45)
axes[1,0].grid(True, alpha=0.3)

# Overfitting
axes[1,1].bar(comparison_df['Modelo'], comparison_df['Overfitting'], color='orange', alpha=0.7)
axes[1,1].set_title('Overfitting (R² Treino - R² Teste)')
axes[1,1].set_ylabel('Diferença R²')
axes[1,1].tick_params(axis='x', rotation=45)
axes[1,1].grid(True, alpha=0.3)
axes[1,1].axhline(y=0, color='red', linestyle='--', alpha=0.5)

plt.tight_layout()
plt.show()

# Análise de importância das features
# Identificação dos fatores mais relevantes para predição de performance

print("🔍 Analisando importância das features...")

# Extrair importância das features do melhor modelo (se for tree-based)
if hasattr(best_model['pipeline'].named_steps['model'], 'feature_importances_'):
    feature_names = numerical_features + categorical_features + hybrid_features
    importances = best_model['pipeline'].named_steps['model'].feature_importances_
    
    # Criar DataFrame com importâncias
    feature_importance_df = pd.DataFrame({
        'Feature': feature_names,
        'Importância': importances
    }).sort_values('Importância', ascending=False)
    
    print(f"\n📊 Importância das features ({best_model_name}):")
    display(feature_importance_df)
    
    # Visualizar importância das features
    plt.figure(figsize=(12, 6))
    plt.barh(feature_importance_df['Feature'], feature_importance_df['Importância'], color='steelblue', alpha=0.7)
    plt.xlabel('Importância')
    plt.title(f'Importância das Features - {best_model_name}')
    plt.gca().invert_yaxis()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # Insights sobre as features mais importantes
    top_features = feature_importance_df.head(5)
    print("\n🎯 Top 5 fatores mais importantes para performance:")
    for idx, row in top_features.iterrows():
        print(f"   {idx+1}. {row['Feature']}: {row['Importância']:.3f}")
        
else:
    print(f"\n⚠️ {best_model_name} não fornece importância de features diretamente.")
    print("Utilizando análise de correlação como alternativa...")
    
    # Análise de correlação como alternativa
    correlation_with_target = X_encoded.corrwith(y_target).abs().sort_values(ascending=False)
    
    print("\n📊 Correlação absoluta com Performance Score:")
    display(correlation_with_target.to_frame('Correlação Absoluta'))
    
    # Visualizar correlações
    plt.figure(figsize=(12, 6))
    plt.barh(correlation_with_target.index, correlation_with_target.values, color='steelblue', alpha=0.7)
    plt.xlabel('Correlação Absoluta')
    plt.title('Correlação das Features com Performance Score')
    plt.gca().invert_yaxis()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

# Recomendações estratégicas para expansão de óticas especializadas
# Aplicação prática do modelo híbrido para tomada de decisão

print("🎯 Gerando recomendações estratégicas para expansão...")

# Predizer performance para todas as cidades
all_predictions = best_model['pipeline'].predict(X_encoded)
city_features['predicted_performance'] = all_predictions

# Identificar cidades com maior potencial (alto score predito, mas baixa performance atual)
# Estas são oportunidades de expansão
city_features['performance_gap'] = city_features['predicted_performance'] - city_features['performance_score']

# Categorizar cidades por potencial de expansão
def categorize_expansion_potential(row):
    if row['predicted_performance'] > 0.5 and row['performance_gap'] > 0.1:
        return 'Alto Potencial - Expansão Prioritária'
    elif row['predicted_performance'] > 0.4 and row['performance_gap'] > 0.05:
        return 'Médio Potencial - Considerar Expansão'
    elif row['predicted_performance'] > 0.25:
        return 'Alto Desempenho - Manter/Otimizar'
    else:
        return 'Baixo Potencial - Não Recomendado'

city_features['expansion_category'] = city_features.apply(categorize_expansion_potential, axis=1)

# Resumo das categorias
expansion_summary = city_features['expansion_category'].value_counts()
print("\n📊 Categorização das cidades por potencial de expansão:")
for category, count in expansion_summary.items():
    percentage = (count / len(city_features)) * 100
    print(f"   • {category}: {count} cidades ({percentage:.1f}%)")

# Top recomendações para expansão
high_potential_cities = city_features[
    city_features['expansion_category'] == 'Alto Desempenho - Manter/Otimizar'
].nlargest(10, 'predicted_performance')[[
    'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'cluster', 
    'predicted_performance', 'performance_score', 'performance_gap',
    'receita_total', 'num_clientes_unicos', 'regiao'
]]

print("\n🏆 TOP 10 CIDADES RECOMENDADAS PARA EXPANSÃO:")
print("(Alto potencial predito com gap de performance atual)")
display(high_potential_cities)

# Análise por cluster
cluster_expansion = city_features.groupby('cluster').agg({
    'expansion_category': lambda x: x.value_counts().index[0],  # Categoria mais comum
    'predicted_performance': 'mean',
    'performance_gap': 'mean',
    'Dim_Lojas.Cidade_Emp': 'count',
    'regiao': lambda x: x.mode().iloc[0] if not x.mode().empty else 'Mista'
}).round(3)

cluster_expansion.columns = ['Categoria_Predominante', 'Performance_Predita_Média', 
                           'Gap_Médio', 'Num_Cidades', 'Região_Predominante']

print("\n🎯 Análise por cluster de mercado:")
display(cluster_expansion)

# Visualizar distribuição geográfica das recomendações
plt.figure(figsize=(15, 8))

# Subplot 1: Performance predita por região
plt.subplot(2, 2, 1)
region_performance = city_features.groupby('regiao')['predicted_performance'].mean().sort_values(ascending=False)
plt.bar(region_performance.index, region_performance.values, color='skyblue', alpha=0.7)
plt.title('Performance Predita Média por Região')
plt.ylabel('Performance Predita')
plt.xticks(rotation=45)
plt.grid(True, alpha=0.3)

# Subplot 2: Distribuição de categorias de expansão
plt.subplot(2, 2, 2)
expansion_summary.plot(kind='pie', autopct='%1.1f%%', startangle=90)
plt.title('Distribuição das Categorias de Expansão')
plt.ylabel('')

# Subplot 3: Performance gap por cluster
plt.subplot(2, 2, 3)
cluster_gap = city_features.groupby('cluster')['performance_gap'].mean()
plt.bar(cluster_gap.index, cluster_gap.values, color='lightcoral', alpha=0.7)
plt.title('Gap de Performance Médio por Cluster')
plt.xlabel('Cluster')
plt.ylabel('Performance Gap')
plt.grid(True, alpha=0.3)

# Subplot 4: Scatter plot: Performance atual vs Predita
plt.subplot(2, 2, 4)
colors = ['red' if cat == 'Alto Potencial - Expansão Prioritária' else 'blue' 
          for cat in city_features['expansion_category']]
plt.scatter(city_features['performance_score'], city_features['predicted_performance'], 
           c=colors, alpha=0.6)
plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)  # Linha diagonal
plt.xlabel('Performance Atual')
plt.ylabel('Performance Predita')
plt.title('Performance Atual vs Predita\n(Vermelho = Alto Potencial)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()