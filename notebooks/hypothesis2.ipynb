{"cells": [{"cell_type": "markdown", "id": "b23b8d5c", "metadata": {}, "source": ["## Hipótese para problemática 2 — Público Ideal\n", "\n", "**Hipótese de Pesquisa**  \n", "- H₀: A idade do cliente **está associada** ao ticket médio.  \n", "- H₁: A idade do cliente **não está associada** ao ticket médio.  \n", "\n", "**Justificativa**  \n", "Analisar a relação entre idade e gasto médio permite identificar se determinados grupos geracionais representam o \"público ideal\".\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b38cc8b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                      sum_sq       df          F        PR(>F)\n", "C(<PERSON>aixa_Idade)  4.182982e+06      8.0  11.411682  1.571510e-14\n", "Residual        8.411925e+08  18359.0        NaN           NaN\n", "\n", "Estatística F: 11.4117\n", "P-valor (PR(>F)): 0.000000\n", "Nível de significância (α): 0.05\n", "\n", "HIPÓTESES:\n", "H₀: A idade do cliente está associada ao ticket médio\n", "H₁: A idade do cliente não está associada ao ticket médio\n", "\n", "RESULTADO ANOVA: REJEITAMOS H₁\n", "Conclusão: Rejeitamos a hipótese de que 'a idade não está associada ao ticket médio'\n", "Com p-valor = 0.000000 < 0.05, ACEITAMOS H₀:\n", "A idade do cliente ESTÁ ASSOCIADA ao ticket médio.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/Python/3.13/lib/python/site-packages/statsmodels/base/model.py:1894: ValueWarning: covariance of constraints does not have full rank. The number of constraints is 8, but rank is 7\n", "  warnings.warn('covariance of constraints does not have full '\n"]}], "source": ["from scipy import stats\n", "import statsmodels.api as sm\n", "from statsmodels.formula.api import ols\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')\n", "\n", "# Pré-processamento\n", "# Converter data de nascimento para datetime\n", "df[\"Dim_Cliente.Data_Nascimento\"] = pd.to_datetime(df[\"Dim_Cliente.Data_Nascimento\"], errors=\"coerce\")\n", "\n", "# Calcular idade (com base em 2025 como referência)\n", "df[\"Idade\"] = 2025 - df[\"Dim_Cliente.Data_Nascimento\"].dt.year\n", "\n", "# C<PERSON>r coluna Ticket_Medio (valor por item)\n", "df[\"Ticket_Medio\"] = df[\"Valor_Total\"] / df[\"Quantidade\"]\n", "\n", "# Remover registros inválidos\n", "valid_age_ticket = df.dropna(subset=[\"Idade\", \"Ticket_Medio\"]).copy()\n", "\n", "# C<PERSON>r faixas et<PERSON>s\n", "valid_age_ticket[\"Faixa_Idade\"] = pd.cut(\n", "    valid_age_ticket[\"Idade\"],\n", "    bins=[10,20,30,40,50,60,70,80,90,100],\n", "    labels=[\"10-19\",\"20-29\",\"30-39\",\"40-49\",\"50-59\",\"60-69\",\"70-79\",\"80-89\",\"90-99\"]\n", ")\n", "\n", "# ANOVA\n", "modelo = ols(\"Ticket_Medio ~ C(Faixa_Idade)\", data=valid_age_ticket).fit()\n", "anova_table = sm.stats.anova_lm(modelo, typ=2)\n", "\n", "print(anova_table)\n", "print()\n", "\n", "# Extrair valores para interpretação\n", "f_value = anova_table.iloc[0]['F']\n", "p_value = anova_table.iloc[0]['PR(>F)']\n", "\n", "print(f\"Estatística F: {f_value:.4f}\")\n", "print(f\"P-valor (PR(>F)): {p_value:.6f}\")\n", "print(f\"Nível de significância (α): 0.05\")\n", "print()\n", "\n", "# Interpretação do ANOVA\n", "print(\"HIPÓTESES:\")\n", "print(\"H₀: A idade do cliente está associada ao ticket médio\")\n", "print(\"H₁: A idade do cliente não está associada ao ticket médio\")\n", "print()\n", "\n", "if p_value < 0.05:\n", "    print(\"RESULTADO ANOVA: REJEITAMOS H₁\")\n", "    print(\"Conclusão: Rejeitamos a hipótese de que 'a idade não está associada ao ticket médio'\")\n", "    print(f\"Com p-valor = {p_value:.6f} < 0.05, ACEITAMOS H₀:\")\n", "    print(\"A idade do cliente ESTÁ ASSOCIADA ao ticket médio.\")\n", "else:\n", "    print(\"RESULTADO ANOVA: NÃO REJEITAMOS H₁\")\n", "    print(\"Conclusão: Não temos evidência suficiente para rejeitar H₁\")\n", "    print(f\"Com p-valor = {p_value:.6f} ≥ 0.05, mantemos H₁:\")\n", "    print(\"A idade do cliente NÃO está associada ao ticket médio.\")"]}, {"cell_type": "markdown", "id": "d1bfc4c8", "metadata": {}, "source": ["**Interpretação dos Resultados:**  \n", "\n", "## RESULTADOS DOS TESTES\n", "\n", "### ANOVA:\n", "- **F = 11.41, p < 0.001** (altamente significativo)\n", "- **Decisão**: Rejeitamos H₁ e aceitamos H₀\n", "- **Conclusão**: A idade do cliente **está associada** ao ticket médio (estatisticamente significativo)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}