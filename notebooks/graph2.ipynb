{"cells": [{"cell_type": "markdown", "id": "bf1c8597", "metadata": {}, "source": ["# Análise de Compras por Faixa Etária e Grupo de Produto\n", "\n", "Este notebook calcula a idade dos clientes a partir da data de nascimento, classifica-os em faixas etárias e, em seguida, gera uma visualização que mostra quais grupos de produtos são mais consumidos em cada faixa de idade.\n", "\n", "O resultado final é um gráfico de barras empilhadas, que permite analisar a distribuição de compras de acordo com a geração do cliente."]}, {"cell_type": "markdown", "id": "16396a59", "metadata": {}, "source": ["## 1. Preparação dos Dados\n", "\n", "- Conversão da coluna `Dim_Cliente.Data_Nascimento` para o formato datetime.  \n", "- <PERSON><PERSON><PERSON><PERSON><PERSON> da idade em anos, usando a diferença entre a data atual e a data de nascimento.  \n", "- Exclusão de clientes com menos de 18 anos.  \n", "\n", "## 2. Defin<PERSON><PERSON> das Faixas Etárias\n", "\n", "As idades foram agrupadas nos seguintes intervalos:\n", "\n", "- 18–29  \n", "- 30–39  \n", "- 40–49  \n", "- 50–59  \n", "- 60–69  \n", "- 70–79  \n", "- 80+  \n", "\n", "<PERSON><PERSON> é feito com `pd.cut`, criando uma coluna categórica chamada **faixa_etaria**.\n", "\n", "## 3. Agrupamento por Faixa e Produto\n", "\n", "Os dados foram agregados com `groupby`, contabilizando a quantidade de registros por **faixa_etaria × Dim_Produtos.Grupo_Produto**.  \n", "Em seguida, a tabela foi transformada em formato matricial (`pivot`) para facilitar a construção do gráfico.\n", "\n", "## 4. Visualização\n", "\n", "- Gráfico de barras empilhadas (`stacked=True`) para mostrar a composição de cada faixa etária em termos de grupo de produto.  \n", "- Paleta de cores `tab20c` para distinguir cada categoria de produto.  \n", "- Legenda posicionada fora da área do gráfico, organizada em **3 colunas**, para evitar sobreposição.  \n", "\n", "Esse gráfico revela quais **grupos de produtos** são mais consumidos em cada faixa etária, permitindo insights sobre preferências de compra por geração.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f05f5163", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 2, "id": "a3664c64", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "# Converter coluna de data de nascimento para datetime (se já não estiver)\n", "df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'])\n", "\n", "# Data de referência (hoje) usada para calcular idades\n", "hoje = pd.to_datetime(datetime.today().date())\n", "\n", "# Selecionar apenas colunas relevantes para este cálculo: grupo de produto e data de nascimento\n", "df_new = df[['Dim_Produtos.Grupo_Produto', 'Dim_Cliente.Data_Nascimento']].copy()\n", "\n", "# Calcular a idade inteira em anos (aproximação simples usando dias // 365)\n", "df_new['idade'] = (hoje - df_new['Dim_Cliente.Data_Nascimento']).dt.days // 365\n", "\n", "# Excluir registros de menores de 18 anos\n", "df_new = df_new[df_new['idade'] > 17]"]}, {"cell_type": "code", "execution_count": 3, "id": "05010078", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/dq/83qxsj710bbgjhs2_rywgz900000gn/T/ipykernel_60784/3286330854.py:10: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  agrupado = df_new.groupby(['faixa_etaria', 'Dim_Produtos.Grupo_Produto']).size().reset_index(name='qtd')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>faixa_etaria</th>\n", "      <th>Dim_Produtos.Grupo_Produto</th>\n", "      <th>qtd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18-29</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>1218</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18-29</td>\n", "      <td>ASSIST TEC REL REVENDA</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18-29</td>\n", "      <td>BAG</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>18-29</td>\n", "      <td>BRINDES</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>18-29</td>\n", "      <td>GRAU</td>\n", "      <td>447</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>18-29</td>\n", "      <td>LENTE CONTATO GRAU</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>18-29</td>\n", "      <td>LENTES VISTA</td>\n", "      <td>290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>18-29</td>\n", "      <td>MATERIAIS CONSUMIVEIS</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>18-29</td>\n", "      <td>MATERIAIS VENDAVEIS</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>18-29</td>\n", "      <td>MULTI LENTE</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>18-29</td>\n", "      <td>OCULOS</td>\n", "      <td>2027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>18-29</td>\n", "      <td>RELOGIOS</td>\n", "      <td>163</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>30-39</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>1822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>30-39</td>\n", "      <td>ASSIST TEC REL REVENDA</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>30-39</td>\n", "      <td>BAG</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>30-39</td>\n", "      <td>BRINDES</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>30-39</td>\n", "      <td>GRAU</td>\n", "      <td>574</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>30-39</td>\n", "      <td>LENTE CONTATO GRAU</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>30-39</td>\n", "      <td>LENTES VISTA</td>\n", "      <td>377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>30-39</td>\n", "      <td>MATERIAIS CONSUMIVEIS</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   faixa_etaria Dim_Produtos.Grupo_Produto   qtd\n", "0         18-29                 ACESSORIOS  1218\n", "1         18-29     ASSIST TEC REL REVENDA    23\n", "2         18-29                        BAG     7\n", "3         18-29                    BRINDES     0\n", "4         18-29                       GRAU   447\n", "5         18-29         LENTE CONTATO GRAU    40\n", "6         18-29               LENTES VISTA   290\n", "7         18-29      MA<PERSON>RIAIS CONSUMIVEIS     5\n", "8         18-29        MATERIAIS VENDAVEIS     3\n", "9         18-29                MULTI LENTE     3\n", "10        18-29                     OCULOS  2027\n", "11        18-29                   RELOGIOS   163\n", "12        30-39                 ACESSORIOS  1822\n", "13        30-39     ASSIST TEC REL REVENDA    40\n", "14        30-39                        BAG    12\n", "15        30-39                    BRINDES     0\n", "16        30-39                       GRAU   574\n", "17        30-39         LENTE CONTATO GRAU    41\n", "18        30-39               LENTES VISTA   377\n", "19        30-39      MATERIAIS CONSUMIVEIS     8"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Definir as faixas de idade (bins) e os rótulos correspondentes\n", "bins = [18, 29, 39, 49, 59, 69, 79, 106]  # limites inferiores e superiores\n", "labels = ['18-29', '30-39', '40-49', '50-59', '60-69', '70-79', '80+']\n", "\n", "# Criar coluna com a faixa etária usando pd.cut\n", "# right=True significa que o limite direito é incluído (ex.: 29 entra na primeira faixa)\n", "df_new['faixa_etaria'] = pd.cut(df_new['idade'], bins=bins, labels=labels, right=True)\n", "\n", "# Agrupar por faixa etária e grupo de produto, contando ocorrências\n", "agrupado = df_new.groupby(['faixa_etaria', 'Dim_Produtos.Grupo_Produto']).size().reset_index(name='qtd')\n", "agrupado.head(20)  # <PERSON><PERSON> as primeiras linhas do agrupamento para inspeção"]}, {"cell_type": "markdown", "id": "d923e90f", "metadata": {}, "source": ["## Interpretação do Gráfico\n", "\n", "- <PERSON>ada barra representa uma faixa etária.  \n", "- A altura total da barra corresponde ao número total de compras naquela faixa.  \n", "- As cores empilhadas mostram a contribuição de cada **grupo de produto**.  \n", "\n", "<PERSON><PERSON><PERSON>, é possível identificar:\n", "- Quais faixas etárias têm maior volume de compras.  \n", "- Quais produtos dominam em cada faixa (ex.: óculos entre jovens adultos, multifocais entre faixas acima de 40 anos).  \n", "- Diferenças de portfólio entre gerações.  "]}, {"cell_type": "code", "execution_count": 4, "id": "433071f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 1400x700 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Pivotar o dataframe 'agrupado' para ter faixas etárias nas linhas e grupos de produto nas colunas\n", "pivot = agrupado.pivot(index=\"faixa_etaria\", \n", "                       columns=\"Dim_Produtos.Grupo_Produto\", \n", "                       values=\"qtd\").fillna(0)\n", "\n", "# Escolher uma paleta de cores com base no número de colunas (grupos de produto)\n", "colors = sns.color_palette(\"tab20c\", n_colors=len(pivot.columns))\n", "\n", "# Criar figura e plotar um gráfico de barras empilhadas\n", "plt.figure(figsize=(14,7))\n", "pivot.plot(\n", "    kind=\"bar\", \n", "    stacked=True, \n", "    figsize=(14,7), \n", "    color=colors, \n", "    linewidth=0.5,\n", ")\n", "\n", "# Ajustar títulos e rótulos para melhor leitura\n", "plt.title(\"Distribuição de Compras por Faixa Etária e Grupo de Produto\", fontsize=14, weight=\"bold\")\n", "plt.xlabel(\"Faixa Etária\", fontsize=12)\n", "plt.ylabel(\"Quantidade\", fontsize=12)\n", "plt.xticks(rotation=0)\n", "\n", "# Posicionar a legenda fora do gráfico para evitar sobreposição\n", "plt.legend(title=\"Grupo de Produto\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", ncol=3)\n", "\n", "plt.tight_layout()\n", "plt.show()  # Exibir o gráfico empilhado"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}