{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Análise de Escalonamento de Variáveis Quantitativas\n", "\n", "Este notebook realiza a análise de escalonamento para três variáveis quantitativas do dataset da Chilli Beans:\n", "- `Valor_Total`: Valor total da transação\n", "- `Quantidade`: Quantidade de itens vendidos\n", "- `Preco_Custo`: Preço de custo do produto\n", "\n", "## Objetivos\n", "1. <PERSON><PERSON><PERSON> as distribuições das variáveis\n", "2. Determinar o método de escalonamento apropriado para cada variável\n", "3. Cal<PERSON> as estatísticas necessárias para o escalonamento\n", "4. <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "5. Visualizar os resultados antes e depois do escalonamento\n", "6. <PERSON><PERSON><PERSON> comparativas"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Importar bibliotecas necessárias\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configurações de visualização\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados iniciais: 40,291 registros\n", "Remoção DEVOLUCAO DE MERCADORIA: 1,281 registros removidos\n", "Preço Varejo > 1: 467 registros removidos\n", "Total Preço Varejo > 1: 0 registros removidos\n", "Validação cálculo valor total: 0 registros removidos\n", "Validação valor total (tolerância 2): 0 registros removidos\n", "Idade entre 10 e 100 anos: 19,255 registros removidos\n", "Idade >= 18 anos no cadastro: 594 registros removidos\n", "Remoção duplicatas: 96 registros removidos\n", "Lojas com Tipo PDV válido: 231 registros removidos\n", "Produtos com nome válido: 0 registros removidos\n", "Produtos com grupo válido: 0 registros removidos\n", "Limpeza de espaços em branco nas colunas categóricas concluída\n", "Regras de negócios aplicadas\n", "\n", "Dados finais: 18,367 registros\n", "\n", "Dataset carregado com 18,367 registros\n", "Colunas disponíveis: ['ID_Faturamento', 'ID_Loja', 'ID_Vendedor', 'ID_Cliente', 'ID_Date', 'ID_Produto', 'Documento', 'DOC_UNICO', 'Transacao', 'Natureza_Operacao', 'Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Num_Vale', 'Desconto', 'Dt_update', 'DESCONTO_CALCULADO', 'ID_Deposito', 'Nome_Tabela_Preco', 'Total_Preco_Varejo', 'Total_Preco_Liquido', 'Dim_Cliente.Bairro_Cliente', 'Dim_Cliente.Cep_Cliente', 'Dim_Cliente.Cidade_cliente', 'Dim_Cliente.Uf_Cliente', 'Dim_Cliente.Pais', 'Dim_Cliente.Sexo', 'Dim_Cliente.Data_Cadastro', 'Dim_Cliente.Data_Nascimento', 'Dim_Cliente.Ativo', 'Dim_Cliente.Estado_Civil', 'Dim_Cliente.Regiao_Cliente', 'Dim_Lojas.Nome_Emp', 'Dim_Lojas.Bairro_Emp', 'Dim_Lojas.Cep_Emp', 'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'Dim_Lojas.Regiao', 'Dim_Lojas.Tipo_PDV', 'Dim_Lojas.Data_Criacao_Emp', 'Dim_Lojas.ID_SAP', 'Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.SAP_NOME', 'Dim_Lojas.REGIAO_CHILLI', 'Dim_Lojas.Cod_Franqueado', 'Dim_Produtos.Cod_Auxiliar', 'Dim_Produtos.Referencia', 'Dim_Produtos.Nome', 'Dim_Produtos.Grupo_Produto', 'Dim_Produtos.Sub_Grupo', 'Dim_Produtos.Cor1', 'Dim_Produtos.Cor2', 'Dim_Produtos.Material1', 'Dim_Produtos.Material2', 'Dim_Produtos.Segmentacao', 'Dim_Produtos.Shape', 'Dim_Produtos.Formato', 'Dim_Produtos.Sexo', 'Dim_Produtos.Griffe', 'Dim_Produtos.GRUPO_CHILLI', 'diferenca', 'check_total', 'idade', 'idade_cadastro']\n"]}], "source": ["# <PERSON>egar os dados\n", "import sys\n", "import os\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv', verbose=True)\n", "\n", "print(f\"\\nDataset carregado com {len(df):,} registros\")\n", "print(f\"Colunas disponíveis: {df.columns.tolist()}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Variável Valor_Total encontrada\n", "✓ Variável Quantidade encontrada\n", "✓ Variável Preco_Custo encontrada\n", "\n", "Dataset com variáveis de interesse: 18,367 registros\n", "Valores nulos removidos: 0 registros\n"]}], "source": ["# <PERSON><PERSON><PERSON><PERSON> as três variáveis quantitativas de interesse\n", "variables = ['Valor_Total', 'Quantidade', 'Preco_Custo']\n", "\n", "# Verificar se as variáveis existem no dataset\n", "for var in variables:\n", "    if var not in df.columns:\n", "        print(f\"ERRO: Variável {var} não encontrada no dataset\")\n", "    else:\n", "        print(f\"✓ Variável {var} encontrada\")\n", "\n", "# Criar subset com apenas as variáveis de interesse\n", "df_variables = df[variables].copy()\n", "\n", "# Remover valores nulos\n", "df_variables = df_variables.dropna()\n", "\n", "print(f\"\\nDataset com variáveis de interesse: {len(df_variables):,} registros\")\n", "print(f\"Valores nulos removidos: {len(df) - len(df_variables):,} registros\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "ANÁLISE EXPLORATÓRIA DAS VARIÁVEIS QUANTITATIVAS\n", "================================================================================\n", "\n", "Estatísticas Descritivas:\n", "        Valor_Total    Quantidade   Preco_Custo\n", "count  18367.000000  18367.000000  18367.000000\n", "mean     305.629263      1.090216     76.367664\n", "std      316.620765      0.325971     75.193611\n", "min        0.010000      1.000000      1.800000\n", "25%       48.000000      1.000000     11.990000\n", "50%      322.138900      1.000000     73.260000\n", "75%      399.980000      1.000000     99.995000\n", "max    11057.969700     12.000000   1379.160000\n", "\n", "Informações Adicionais:\n", "\n", "Valor_Total:\n", "  - Tipo de dados: float64\n", "  - Valores únicos: 2,833\n", "  - Amplitude: 11057.96\n", "  - Coeficiente de variação: 103.60%\n", "  - Assimetria (skewness): 7.056\n", "  - Curtose (kurtosis): 123.144\n", "\n", "Quantidade:\n", "  - Tipo de dados: int64\n", "  - Valores únicos: 8\n", "  - Amplitude: 11.00\n", "  - Coeficiente de variação: 29.90%\n", "  - Assimetria (skewness): 6.337\n", "  - Curtose (kurtosis): 105.675\n", "\n", "Preco_Custo:\n", "  - Tipo de dados: float64\n", "  - Valores únicos: 1,557\n", "  - Amplitude: 1377.36\n", "  - Coeficiente de variação: 98.46%\n", "  - Assimetria (skewness): 4.440\n", "  - Curtose (kurtosis): 41.604\n"]}], "source": ["# Análise exploratória das variáveis\n", "print(\"=\" * 80)\n", "print(\"ANÁLISE EXPLORATÓRIA DAS VARIÁVEIS QUANTITATIVAS\")\n", "print(\"=\" * 80)\n", "\n", "# Estatísticas descritivas\n", "stats_desc = df_variables.describe()\n", "print(\"\\nEstatísticas Descritivas:\")\n", "print(stats_desc)\n", "\n", "# Informações adicionais\n", "print(\"\\nInformações Adicionais:\")\n", "for var in variables:\n", "    data = df_variables[var]\n", "    print(f\"\\n{var}:\")\n", "    print(f\"  - Tipo de dados: {data.dtype}\")\n", "    print(f\"  - Val<PERSON> únicos: {data.nunique():,}\")\n", "    print(f\"  - Amplitude: {data.max() - data.min():.2f}\")\n", "    print(f\"  - Coeficiente de variação: {(data.std() / data.mean()) * 100:.2f}%\")\n", "    print(f\"  - Assimetria (skewness): {stats.skew(data):.3f}\")\n", "    print(f\"  - Curtose (kurtosis): {stats.kurtosis(data):.3f}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualização das distribuições originais\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('Distribuições das Variáveis Originais', fontsize=16, fontweight='bold')\n", "\n", "for i, var in enumerate(variables):\n", "    # Histograma\n", "    axes[0, i].hist(df_variables[var], bins=50, alpha=0.7, color=sns.color_palette()[i])\n", "    axes[0, i].set_title(f'Histograma - {var}')\n", "    axes[0, i].set_xlabel(var)\n", "    axes[0, i].set_ylabel('Frequência')\n", "    axes[0, i].grid(True, alpha=0.3)\n", "    \n", "    # Box plot\n", "    axes[1, i].boxplot(df_variables[var])\n", "    axes[1, i].set_title(f'Box Plot - {var}')\n", "    axes[1, i].set_ylabel(var)\n", "    axes[1, i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('../assets/distribuicoes_originais.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "ANÁLISE DE NORMALIDADE (Teste de Shapiro-Wilk)\n", "================================================================================\n", "\n", "Valor_Total:\n", "  - Estatística: 0.619135\n", "  - P-valor: 2.25e-74\n", "  - Distribuição normal: Não (α = 0.05)\n", "\n", "Quantidade:\n", "  - Estatística: 0.305381\n", "  - P-valor: 2.03e-87\n", "  - Distribuição normal: Não (α = 0.05)\n", "\n", "Preco_Custo:\n", "  - Estatística: 0.684231\n", "  - P-valor: 1.58e-70\n", "  - Distribuição normal: Não (α = 0.05)\n", "\n", "Nota: Teste realizado com amostra de 5,000 registros\n"]}], "source": ["# Análise de normalidade\n", "print(\"=\" * 80)\n", "print(\"ANÁLISE DE NORMALIDADE (Teste de Shapiro-Wilk)\")\n", "print(\"=\" * 80)\n", "\n", "# Teste de normalidade (usando amostra devido ao tamanho do dataset)\n", "sample_size = min(5000, len(df_variables))  # Shapiro-Wilk tem limitação de tamanho\n", "df_sample = df_variables.sample(n=sample_size, random_state=42)\n", "\n", "normality_results = {}\n", "for var in variables:\n", "    stat, p_value = stats.shapiro(df_sample[var])\n", "    normality_results[var] = {'statistic': stat, 'p_value': p_value}\n", "    \n", "    print(f\"\\n{var}:\")\n", "    print(f\"  - Estatística: {stat:.6f}\")\n", "    print(f\"  - P-valor: {p_value:.2e}\")\n", "    print(f\"  - Distribuição normal: {'Não' if p_value < 0.05 else 'Sim'} (α = 0.05)\")\n", "\n", "print(f\"\\nNota: Teste realizado com amostra de {sample_size:,} registros\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "DETERMINAÇÃO DO MÉTODO DE ESCALONAMENTO\n", "================================================================================\n", "\n", "Valor_Total:\n", "  - Outliers: 383 (2.09%)\n", "  - Assimetria: 7.056\n", "  - Coeficiente de variação: 103.60%\n", "  - Amplitude: 11057.96\n", "  - Método escolhido: STANDARDIZATION\n", "  - Justificativa: Presença significativa de outliers ou alta assimetria\n", "\n", "Quantidade:\n", "  - Outliers: 1,536 (8.36%)\n", "  - Assimetria: 6.337\n", "  - Coeficiente de variação: 29.90%\n", "  - Amplitude: 11.00\n", "  - Método escolhido: STANDARDIZATION\n", "  - Justificativa: Presença significativa de outliers ou alta assimetria\n", "\n", "Preco_Custo:\n", "  - Outliers: 402 (2.19%)\n", "  - Assimetria: 4.440\n", "  - Coeficiente de variação: 98.46%\n", "  - Amplitude: 1377.36\n", "  - Método escolhido: STANDARDIZATION\n", "  - Justificativa: Presença significativa de outliers ou alta assimetria\n"]}], "source": ["# Determinação do método de escalonamento para cada variável\n", "print(\"=\" * 80)\n", "print(\"DETERMINAÇÃO DO MÉTODO DE ESCALONAMENTO\")\n", "print(\"=\" * 80)\n", "\n", "scaling_methods = {}\n", "\n", "for var in variables:\n", "    data = df_variables[var]\n", "    \n", "    # Critérios para decisão:\n", "    # 1. Presença de outliers (IQR method)\n", "    Q1 = data.quantile(0.25)\n", "    Q3 = data.quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "    outliers = data[(data < lower_bound) | (data > upper_bound)]\n", "    outlier_percentage = (len(outliers) / len(data)) * 100\n", "    \n", "    # 2. Assimetria da distribuição\n", "    skewness = stats.skew(data)\n", "    \n", "    # 3. Coeficiente de variação\n", "    cv = (data.std() / data.mean()) * 100\n", "    \n", "    # 4. Amplitude dos dados\n", "    amplitude = data.max() - data.min()\n", "    \n", "    print(f\"\\n{var}:\")\n", "    print(f\"  - Outliers: {len(outliers):,} ({outlier_percentage:.2f}%)\")\n", "    print(f\"  - Assimetria: {skewness:.3f}\")\n", "    print(f\"  - Coeficiente de variação: {cv:.2f}%\")\n", "    print(f\"  - Amplitude: {amplitude:.2f}\")\n", "    \n", "    # Decisão do método de escalonamento\n", "    if outlier_percentage > 5 or abs(skewness) > 1:\n", "        method = 'standardization'  # Z-score é mais robusto a outliers\n", "        reason = \"Presença significativa de outliers ou alta assimetria\"\n", "    else:\n", "        method = 'normalization'  # Min-<PERSON> quando distribuição é mais uniforme\n", "        reason = \"Distribuição relativamente uniforme com poucos outliers\"\n", "    \n", "    scaling_methods[var] = {'method': method, 'reason': reason}\n", "    print(f\"  - M<PERSON>todo escolhido: {method.upper()}\")\n", "    print(f\"  - Justificativa: {reason}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "ESTATÍSTICAS PARA ESCALONAMENTO\n", "================================================================================\n", "\n", "Valor_Total:\n", "  - <PERSON><PERSON> mín<PERSON>: 0.010000\n", "  - Valor máximo: 11057.969700\n", "  - Média: 305.629263\n", "  - Desvio padrão populacional: 316.612146\n", "  - <PERSON>vio padrão amostral: 316.620765\n", "\n", "Quantidade:\n", "  - <PERSON><PERSON> mínimo: 1.000000\n", "  - Valor máximo: 12.000000\n", "  - Média: 1.090216\n", "  - Desvio padrão populacional: 0.325962\n", "  - <PERSON><PERSON> pad<PERSON>ão amostral: 0.325971\n", "\n", "Preco_Custo:\n", "  - Valor mínimo: 1.800000\n", "  - Valor máximo: 1379.160000\n", "  - Média: 76.367664\n", "  - <PERSON>vio padrão populacional: 75.191564\n", "  - <PERSON><PERSON> amostral: 75.193611\n"]}], "source": ["# Cálculo das estatísticas necessárias para escalonamento\n", "print(\"=\" * 80)\n", "print(\"ESTATÍSTICAS PARA ESCALONAMENTO\")\n", "print(\"=\" * 80)\n", "\n", "scaling_stats = {}\n", "\n", "for var in variables:\n", "    data = df_variables[var]\n", "    \n", "    stats_dict = {\n", "        'min': data.min(),\n", "        'max': data.max(),\n", "        'mean': data.mean(),\n", "        'std_pop': data.std(ddof=0),  # <PERSON>vio padrão populacional\n", "        'std_sample': data.std(ddof=1)  # <PERSON><PERSON> pad<PERSON> amostral\n", "    }\n", "    \n", "    scaling_stats[var] = stats_dict\n", "    \n", "    print(f\"\\n{var}:\")\n", "    print(f\"  - <PERSON><PERSON> mín<PERSON>: {stats_dict['min']:.6f}\")\n", "    print(f\"  - <PERSON><PERSON> máximo: {stats_dict['max']:.6f}\")\n", "    print(f\"  - Média: {stats_dict['mean']:.6f}\")\n", "    print(f\"  - <PERSON><PERSON> padrão populacional: {stats_dict['std_pop']:.6f}\")\n", "    print(f\"  - <PERSON><PERSON> pad<PERSON> amostral: {stats_dict['std_sample']:.6f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "EQUAÇÕES MATEMÁTICAS DE ESCALONAMENTO\n", "================================================================================\n", "\n", "Valor_Total - Método: STANDARDIZATION\n", "  Fórmula geral: Z = (X - μ) / σ\n", "  Fórmula específica: Z = (X - 305.629263) / 316.612146\n", "\n", "Quantidade - Método: STANDARDIZATION\n", "  Fórmula geral: Z = (X - μ) / σ\n", "  Fórmula específica: Z = (X - 1.090216) / 0.325962\n", "\n", "Preco_Custo - Método: STANDARDIZATION\n", "  Fórmula geral: Z = (X - μ) / σ\n", "  Fórmula específica: Z = (X - 76.367664) / 75.191564\n"]}], "source": ["# Equações matemáticas de escalonamento\n", "print(\"=\" * 80)\n", "print(\"EQUAÇÕES MATEMÁTICAS DE ESCALONAMENTO\")\n", "print(\"=\" * 80)\n", "\n", "equations = {}\n", "\n", "for var in variables:\n", "    method = scaling_methods[var]['method']\n", "    stats_var = scaling_stats[var]\n", "    \n", "    print(f\"\\n{var} - Método: {method.upper()}\")\n", "    \n", "    if method == 'standardization':\n", "        # Z-score: (x - μ) / σ\n", "        mean = stats_var['mean']\n", "        std = stats_var['std_pop']  # Usando desvio padrão populacional\n", "        \n", "        equation = f\"Z = (X - {mean:.6f}) / {std:.6f}\"\n", "        print(f\"  Fórmula geral: Z = (X - μ) / σ\")\n", "        print(f\"  Fórmula específica: {equation}\")\n", "        \n", "        equations[var] = {\n", "            'method': 'standardization',\n", "            'formula': equation,\n", "            'mean': mean,\n", "            'std': std\n", "        }\n", "        \n", "    else:  # normalization\n", "        # Min-Max: (x - min) / (max - min)\n", "        min_val = stats_var['min']\n", "        max_val = stats_var['max']\n", "        \n", "        equation = f\"X_norm = (X - {min_val:.6f}) / ({max_val:.6f} - {min_val:.6f})\"\n", "        print(f\"  Fórmula geral: X_norm = (X - min) / (max - min)\")\n", "        print(f\"  Fórmula específica: {equation}\")\n", "        \n", "        equations[var] = {\n", "            'method': 'normalization',\n", "            'formula': equation,\n", "            'min': min_val,\n", "            'max': max_val\n", "        }"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "APLICAÇÃO DO ESCALONAMENTO\n", "================================================================================\n", "\n", "Valor_Total - STANDARDIZATION aplicado\n", "  - Dados originais: min=0.010, max=11057.970\n", "  - Dados escalonados: min=-0.965, max=33.961\n", "  - Média escalonada: 0.000000\n", "  - <PERSON><PERSON> escalonado: 1.000027\n", "\n", "Quantidade - STANDARDIZATION aplicado\n", "  - Dados originais: min=1.000, max=12.000\n", "  - Dados escalonados: min=-0.277, max=33.470\n", "  - Média escalonada: 0.000000\n", "  - <PERSON><PERSON> escalonado: 1.000027\n", "\n", "Preco_Custo - STANDARDIZATION aplicado\n", "  - Dados originais: min=1.800, max=1379.160\n", "  - Dados escalonados: min=-0.992, max=17.326\n", "  - Média escalonada: 0.000000\n", "  - <PERSON><PERSON> escalonado: 1.000027\n", "\n", "Dataset escalonado criado com 18367 registros\n"]}], "source": ["# Aplicação do escalonamento\n", "print(\"=\" * 80)\n", "print(\"APLICAÇÃO DO ESCALONAMENTO\")\n", "print(\"=\" * 80)\n", "\n", "# C<PERSON>r cópia dos dados originais\n", "df_scaled = df_variables.copy()\n", "\n", "# Aplicar escalonamento para cada variável\n", "scalers = {}\n", "\n", "for var in variables:\n", "    method = scaling_methods[var]['method']\n", "    \n", "    if method == 'standardization':\n", "        scaler = StandardScaler()\n", "        df_scaled[f'{var}_scaled'] = scaler.fit_transform(df_variables[[var]]).flatten()\n", "    else:  # normalization\n", "        scaler = MinMaxScaler()\n", "        df_scaled[f'{var}_scaled'] = scaler.fit_transform(df_variables[[var]]).flatten()\n", "    \n", "    scalers[var] = scaler\n", "    \n", "    print(f\"\\n{var} - {method.upper()} aplicado\")\n", "    print(f\"  - Dad<PERSON> originais: min={df_variables[var].min():.3f}, max={df_variables[var].max():.3f}\")\n", "    print(f\"  - Dados escalonados: min={df_scaled[f'{var}_scaled'].min():.3f}, max={df_scaled[f'{var}_scaled'].max():.3f}\")\n", "    print(f\"  - Média escalonada: {df_scaled[f'{var}_scaled'].mean():.6f}\")\n", "    print(f\"  - <PERSON><PERSON> escalonado: {df_scaled[f'{var}_scaled'].std():.6f}\")\n", "\n", "print(f\"\\nDataset escalonado criado com {len(df_scaled)} registros\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualização: Histogramas antes e depois do escalonamento\n", "fig, axes = plt.subplots(3, 2, figsize=(16, 18))\n", "fig.suptitle('Comparação: Distribuições Antes e Depois do Escalonamento', fontsize=16, fontweight='bold')\n", "\n", "for i, var in enumerate(variables):\n", "    # Histograma original\n", "    axes[i, 0].hist(df_variables[var], bins=50, alpha=0.7, color=sns.color_palette()[i], edgecolor='black')\n", "    axes[i, 0].set_title(f'{var} - Original', fontweight='bold')\n", "    axes[i, 0].set_xlabel(var)\n", "    axes[i, 0].set_ylabel('Frequência')\n", "    axes[i, 0].grid(True, alpha=0.3)\n", "    \n", "    # Adicionar estatísticas no gráfico original\n", "    mean_orig = df_variables[var].mean()\n", "    std_orig = df_variables[var].std()\n", "    axes[i, 0].axvline(mean_orig, color='red', linestyle='--', linewidth=2, label=f'Média: {mean_orig:.2f}')\n", "    axes[i, 0].legend()\n", "    \n", "    # Histograma escalonado\n", "    scaled_var = f'{var}_scaled'\n", "    axes[i, 1].hist(df_scaled[scaled_var], bins=50, alpha=0.7, color=sns.color_palette()[i+3], edgecolor='black')\n", "    method_name = scaling_methods[var]['method'].title()\n", "    axes[i, 1].set_title(f'{var} - Após {method_name}', fontweight='bold')\n", "    axes[i, 1].set_xlabel(f'{var} (escalonado)')\n", "    axes[i, 1].set_ylabel('Frequência')\n", "    axes[i, 1].grid(True, alpha=0.3)\n", "    \n", "    # Adicionar estatísticas no gráfico escalonado\n", "    mean_scaled = df_scaled[scaled_var].mean()\n", "    std_scaled = df_scaled[scaled_var].std()\n", "    axes[i, 1].axvline(mean_scaled, color='red', linestyle='--', linewidth=2, label=f'Média: {mean_scaled:.3f}')\n", "    axes[i, 1].legend()\n", "\n", "plt.tight_layout()\n", "plt.savefig('../assets/histogramas_comparacao.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "TABELAS COMPARATIVAS - PRIMEIROS 10 REGISTROS\n", "================================================================================\n", "\n", "Tabela 1: <PERSON><PERSON> (Primeiros 10 registros)\n", "------------------------------------------------------------\n", "    Valor_Total  Quantidade  Preco_Custo\n", "1       20.9680           1         7.00\n", "2       63.0000           1        53.67\n", "3       58.0000           1        53.67\n", "4       70.0000           1        53.67\n", "5       63.0000           1        53.67\n", "6       53.0000           1        47.06\n", "7       20.0000           1         7.00\n", "8       16.3964           1         7.00\n", "9       19.9800           1         5.22\n", "10     119.8800           6         5.22\n", "\n", "\n", "Tabela 2: <PERSON><PERSON> (Primeiros 10 registros)\n", "------------------------------------------------------------\n", "    Valor_Total (Escalonado)  Quantidade (Escalonado)  Preco_Custo (Escalonado)\n", "1                  -0.899085                -0.276769                 -0.922546\n", "2                  -0.766330                -0.276769                 -0.301864\n", "3                  -0.782122                -0.276769                 -0.301864\n", "4                  -0.744221                -0.276769                 -0.301864\n", "5                  -0.766330                -0.276769                 -0.301864\n", "6                  -0.797914                -0.276769                 -0.389773\n", "7                  -0.902142                -0.276769                 -0.922546\n", "8                  -0.913524                -0.276769                 -0.922546\n", "9                  -0.902206                -0.276769                 -0.946219\n", "10                 -0.586678                15.062459                 -0.946219\n", "\n", "\n", "Tabelas salvas em CSV para documentação.\n"]}], "source": ["# Tabelas comparativas: Primeiros 10 registros\n", "print(\"=\" * 80)\n", "print(\"TABELAS COMPARATIVAS - PRIMEIROS 10 REGISTROS\")\n", "print(\"=\" * 80)\n", "\n", "# Tabela 1: <PERSON><PERSON>\n", "print(\"\\nTabela 1: <PERSON><PERSON> (Primeiros 10 registros)\")\n", "print(\"-\" * 60)\n", "original_table = df_variables[variables].head(10).round(6)\n", "original_table.index = range(1, 11)  # Índice começando em 1\n", "print(original_table.to_string())\n", "\n", "# Tabela 2: <PERSON><PERSON> es<PERSON>\n", "print(\"\\n\\nTabela 2: <PERSON><PERSON> (Primeiros 10 registros)\")\n", "print(\"-\" * 60)\n", "scaled_columns = [f'{var}_scaled' for var in variables]\n", "scaled_table = df_scaled[scaled_columns].head(10).round(6)\n", "scaled_table.index = range(1, 11)  # Índice começando em 1\n", "# Renomear colunas para melhor visualização\n", "scaled_table.columns = [col.replace('_scaled', ' (E<PERSON><PERSON><PERSON>)') for col in scaled_table.columns]\n", "print(scaled_table.to_string())\n", "\n", "# Salvar tabelas em CSV para documentação\n", "original_table.to_csv('../assets/tabela_dados_originais.csv')\n", "scaled_table.to_csv('../assets/tabela_dados_escalonados.csv')\n", "print(\"\\n\\nTabelas salvas em CSV para documentação.\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "RESUMO FINAL DA ANÁLISE DE ESCALONAMENTO\n", "================================================================================\n", "\n", "Resumo Comparativo:\n", "   Variável          Método                                         Justificativa Min Original Max Original Média Original Min Escalonado Max Escalonado Média Escalonada\n", "Valor_Total Standardization Presença significativa de outliers ou alta assimetria        0.010    11057.970        305.629         -0.965         33.961            0.000\n", " Quantidade Standardization Presença significativa de outliers ou alta assimetria        1.000       12.000          1.090         -0.277         33.470            0.000\n", "Preco_Custo Standardization Presença significativa de outliers ou alta assimetria        1.800     1379.160         76.368         -0.992         17.326            0.000\n", "\n", "\n", "Resumo salvo em '../assets/resumo_escalonamento.csv'\n", "\n", "================================================================================\n", "ANÁLISE DE ESCALONAMENTO CONCLUÍDA COM SUCESSO!\n", "================================================================================\n"]}], "source": ["# Resumo final e validação\n", "print(\"=\" * 80)\n", "print(\"RESUMO FINAL DA ANÁLISE DE ESCALONAMENTO\")\n", "print(\"=\" * 80)\n", "\n", "summary_data = []\n", "\n", "for var in variables:\n", "    method = scaling_methods[var]['method']\n", "    reason = scaling_methods[var]['reason']\n", "    \n", "    original_stats = {\n", "        'min': df_variables[var].min(),\n", "        'max': df_variables[var].max(),\n", "        'mean': df_variables[var].mean(),\n", "        'std': df_variables[var].std()\n", "    }\n", "    \n", "    scaled_stats = {\n", "        'min': df_scaled[f'{var}_scaled'].min(),\n", "        'max': df_scaled[f'{var}_scaled'].max(),\n", "        'mean': df_scaled[f'{var}_scaled'].mean(),\n", "        'std': df_scaled[f'{var}_scaled'].std()\n", "    }\n", "    \n", "    summary_data.append({\n", "        'Variável': var,\n", "        'Método': method.title(),\n", "        'Justificativa': reason,\n", "        'Min Original': f\"{original_stats['min']:.3f}\",\n", "        'Max Original': f\"{original_stats['max']:.3f}\",\n", "        'Média Original': f\"{original_stats['mean']:.3f}\",\n", "        'Min Escalonado': f\"{scaled_stats['min']:.3f}\",\n", "        '<PERSON> E<PERSON>lonado': f\"{scaled_stats['max']:.3f}\",\n", "        'Média E<PERSON>ada': f\"{scaled_stats['mean']:.3f}\"\n", "    })\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "print(\"\\nResumo Comparativo:\")\n", "print(summary_df.to_string(index=False))\n", "\n", "# <PERSON><PERSON> resumo\n", "summary_df.to_csv('../assets/resumo_escalonamento.csv', index=False)\n", "print(\"\\n\\nResumo salvo em '../assets/resumo_escalonamento.csv'\")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"ANÁLISE DE ESCALONAMENTO CONCLUÍDA COM SUCESSO!\")\n", "print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset com dados escalonados salvo em '../assets/dados_escalonados.csv'\n", "Informações de escalonamento salvas em '../assets/scaling_info.json'\n", "\n", "Análise completa! Todos os arquivos foram gerados para a documentação.\n"]}], "source": ["# Salvar dados escalonados para uso posterior\n", "df_scaled.to_csv('../assets/dados_escalonados.csv', index=False)\n", "print(\"Dataset com dados escalonados salvo em '../assets/dados_escalonados.csv'\")\n", "\n", "# Salvar informações de escalonamento para documentação\n", "import json\n", "\n", "scaling_info = {\n", "    'variables': variables,\n", "    'methods': scaling_methods,\n", "    'statistics': scaling_stats,\n", "    'equations': equations,\n", "    'dataset_info': {\n", "        'total_records': len(df_scaled),\n", "        'original_records': len(df),\n", "        'records_used': len(df_variables)\n", "    }\n", "}\n", "\n", "# Converter numpy types para tipos Python nativos para JSON\n", "def convert_numpy_types(obj):\n", "    if isinstance(obj, np.integer):\n", "        return int(obj)\n", "    elif isinstance(obj, np.floating):\n", "        return float(obj)\n", "    elif isinstance(obj, np.n<PERSON>ray):\n", "        return obj.tolist()\n", "    elif isinstance(obj, dict):\n", "        return {key: convert_numpy_types(value) for key, value in obj.items()}\n", "    elif isinstance(obj, list):\n", "        return [convert_numpy_types(item) for item in obj]\n", "    return obj\n", "\n", "scaling_info_clean = convert_numpy_types(scaling_info)\n", "\n", "with open('../assets/scaling_info.json', 'w', encoding='utf-8') as f:\n", "    json.dump(scaling_info_clean, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"Informações de escalonamento salvas em '../assets/scaling_info.json'\")\n", "print(\"\\nAnálise completa! Todos os arquivos foram gerados para a documentação.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}