{"cells": [{"cell_type": "markdown", "id": "2526dd69", "metadata": {}, "source": ["## Hipótese para problemática 1 — Fluxo e Conversão das Óticas de Rua\n", "\n", "**Hipótese de Pesquisa**  \n", "- H₀: O tipo de PDV **influencia** a quantidade vendida.  \n", "- H₁: O tipo de PDV **não influencia** significativamente a quantidade vendida.  \n", "\n", "**Justificativa**  \n", "As lojas de rua são um modelo novo e podem ter comportamento de vendas distinto em relação a shoppings, quiosques e Eco. Validar estatisticamente essa hipótese ajuda a entender a performance de cada canal."]}, {"cell_type": "code", "execution_count": 1, "id": "649ce373", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Tipo_PDV</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON>_<PERSON></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Máximo</th>\n", "      <th>Total_Vendido</th>\n", "      <th>Contagem</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ECO CHILLI</td>\n", "      <td>1.027190</td>\n", "      <td>1.0</td>\n", "      <td>0.162884</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>340</td>\n", "      <td>331</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ECO CHILLI HÍBRIDO</td>\n", "      <td>1.011905</td>\n", "      <td>1.0</td>\n", "      <td>0.109109</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>85</td>\n", "      <td>84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>LOJA</td>\n", "      <td>1.072289</td>\n", "      <td>1.0</td>\n", "      <td>0.302442</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>7209</td>\n", "      <td>6723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>LOJA CONCEPT</td>\n", "      <td>1.135135</td>\n", "      <td>1.0</td>\n", "      <td>0.343418</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>126</td>\n", "      <td>111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LOJA DE RUA</td>\n", "      <td>1.050193</td>\n", "      <td>1.0</td>\n", "      <td>0.235819</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>272</td>\n", "      <td>259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>LOJA DE RUA HIBRIDO</td>\n", "      <td>1.072058</td>\n", "      <td>1.0</td>\n", "      <td>0.285148</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>2678</td>\n", "      <td>2498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>LOJA DE RUA OTICO</td>\n", "      <td>1.270833</td>\n", "      <td>1.0</td>\n", "      <td>0.468465</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>244</td>\n", "      <td>192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>LOJA DE RUA OUTLET</td>\n", "      <td>1.125000</td>\n", "      <td>1.0</td>\n", "      <td>0.353553</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>LOJA HIBRIDO</td>\n", "      <td>1.039548</td>\n", "      <td>1.0</td>\n", "      <td>0.222156</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>736</td>\n", "      <td>708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>LOJA OTICO</td>\n", "      <td>1.224595</td>\n", "      <td>1.0</td>\n", "      <td>0.453018</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>3555</td>\n", "      <td>2903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>LOJA OUTLET</td>\n", "      <td>1.025000</td>\n", "      <td>1.0</td>\n", "      <td>0.158114</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>QUIOSQUE</td>\n", "      <td>1.048915</td>\n", "      <td>1.0</td>\n", "      <td>0.294090</td>\n", "      <td>1</td>\n", "      <td>12</td>\n", "      <td>3624</td>\n", "      <td>3455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>QUIOSQUE HIBRIDO</td>\n", "      <td>1.025886</td>\n", "      <td>1.0</td>\n", "      <td>0.167267</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>753</td>\n", "      <td>734</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>QUIOSQUE OTICO</td>\n", "      <td>1.104377</td>\n", "      <td>1.0</td>\n", "      <td>0.306265</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>328</td>\n", "      <td>297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>QUIOSQUE OUTLET</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Dim_Lojas.Tipo_PDV     Média  Mediana  Desvio_Padrão  M<PERSON>  \\\n", "0            ECO CHILLI  1.027190      1.0       0.162884       1       2   \n", "1    ECO CHILLI HÍBRIDO  1.011905      1.0       0.109109       1       2   \n", "2                  LOJA  1.072289      1.0       0.302442       1       9   \n", "3          LOJA CONCEPT  1.135135      1.0       0.343418       1       2   \n", "4           LOJA DE RUA  1.050193      1.0       0.235819       1       3   \n", "5   LOJA DE RUA HIBRIDO  1.072058      1.0       0.285148       1       4   \n", "6     LOJA DE RUA OTICO  1.270833      1.0       0.468465       1       3   \n", "7    LOJA DE RUA OUTLET  1.125000      1.0       0.353553       1       2   \n", "8          LOJA HIBRIDO  1.039548      1.0       0.222156       1       4   \n", "9            LOJA OTICO  1.224595      1.0       0.453018       1       5   \n", "10          LOJA OUTLET  1.025000      1.0       0.158114       1       2   \n", "11             QUIOSQUE  1.048915      1.0       0.294090       1      12   \n", "12     QUIOSQUE HIBRIDO  1.025886      1.0       0.167267       1       3   \n", "13       QUIOSQUE OTICO  1.104377      1.0       0.306265       1       2   \n", "14      QUIOSQUE OUTLET  1.000000      1.0       0.000000       1       1   \n", "\n", "    Total_Vendido  Contagem  \n", "0             340       331  \n", "1              85        84  \n", "2            7209      6723  \n", "3             126       111  \n", "4             272       259  \n", "5            2678      2498  \n", "6             244       192  \n", "7               9         8  \n", "8             736       708  \n", "9            3555      2903  \n", "10             41        40  \n", "11           3624      3455  \n", "12            753       734  \n", "13            328       297  \n", "14             24        24  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')\n", "\n", "# Agrupar por tipo de PDV e calcular estatísticas\n", "tabela_pdv = df.groupby(\"Dim_Lojas.Tipo_PDV\")[\"Quantidade\"].agg(\n", "    Média=\"mean\",\n", "    Mediana=\"median\",\n", "    Desvio_Padrão=\"std\",\n", "    Mínimo=\"min\",\n", "    Máximo=\"max\",\n", "    Total_Vendido=\"sum\",\n", "    Contagem=\"count\"\n", ").reset_index()\n", "\n", "# Mostrar a tabela formatada\n", "tabela_pdv"]}, {"cell_type": "code", "execution_count": 2, "id": "ad7d1a09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estatística F: 52.6258\n", "P-valor: 0.000000\n", "Nível de significância (α): 0.05\n", "\n", "HIPÓTESES:\n", "H₀: O tipo de PDV influencia a quantidade vendida\n", "H₁: O tipo de PDV não influencia significativamente a quantidade vendida\n", "\n", "RESULTADO: REJEITAMOS H₁\n", "Conclusão: Rejeitamos a hipótese de que 'o tipo de PDV não influencia a quantidade vendida'\n", "Com p-valor = 0.000000 < 0.05, temos evidência estatística para rejeitar H₁\n"]}], "source": ["# Importar biblioteca para teste estatístico\n", "from scipy.stats import f_oneway\n", "\n", "# Separar os dados por tipo de PDV\n", "grupos_pdv = [group['Quantidade'].values for name, group in df.groupby('Dim_Lojas.Tipo_PDV')]\n", "\n", "# Executar teste ANOVA\n", "estatistica_f, p_valor = f_oneway(*grupos_pdv)\n", "\n", "print(f\"Estatística F: {estatistica_f:.4f}\")\n", "print(f\"P-valor: {p_valor:.6f}\")\n", "print(f\"Nível de significância (α): 0.05\")\n", "print()\n", "\n", "# Lembrete das hipóteses:\n", "print(\"HIPÓTESES:\")\n", "print(\"H₀: O tipo de PDV influencia a quantidade vendida\")\n", "print(\"H₁: O tipo de PDV não influencia significativamente a quantidade vendida\")\n", "print()\n", "\n", "# Interpretação correta focando em H₁\n", "if p_valor < 0.05:\n", "    print(\"RESULTADO: REJEITAMOS H₁\")\n", "    print(\"Conclusão: Rejeitamos a hipótese de que 'o tipo de PDV não influencia a quantidade vendida'\")\n", "    print(f\"Com p-valor = {p_valor:.6f} < 0.05, temos evidência estatística para rejeitar H₁\")\n", "else:\n", "    print(\"RESULTADO: NÃO REJEITAMOS H₁\")\n", "    print(\"Conclusão: Não temos evidência suficiente para rejeitar H₁\")\n", "    print(f\"Com p-valor = {p_valor:.6f} ≥ 0.05, mantemos H₁:\")\n", "    print(\"O tipo de PDV NÃO influencia significativamente a quantidade vendida.\")"]}, {"cell_type": "markdown", "id": "8303ec83", "metadata": {}, "source": ["**Interpretação dos Resultados:**  \n", "\n", "## CONCLUSÃO FINAL\n", "\n", "**O tipo de PDV INFLUENCIA significativamente a quantidade vendida**\n", "\n", "**Evid<PERSON><PERSON><PERSON>:**\n", "- P-valor < 0.001 (muito menor que α = 0.05)\n", "- Estatística F = 52.63 (valor alto indica forte diferença entre grupos)\n", "- Evidência estatística muito forte de que diferentes tipos de PDV têm comportamentos de venda distintos"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}