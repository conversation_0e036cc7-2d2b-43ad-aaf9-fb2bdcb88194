{"cells": [{"cell_type": "markdown", "id": "8a2cc9b5", "metadata": {}, "source": ["# An<PERSON>lise de Ticket Médio por Canal de Venda e Região\n", "\n", "Este notebook realiza uma análise exploratória das vendas em óticas, avaliando a métrica de **ticket médio** por canal de venda e por região do Brasil.\n", "\n", "## 1. Carregamento e Preparação dos Dados\n", "\n", "* Leitura do arquivo CSV de dados processados.\n", "* Aplicação de filtros de negócio usando o módulo `data_filtering`.\n", "* Utilização da coluna `Dim_Lojas.REGIAO_CHILLI` que já contém o mapeamento das regiões do Brasil.\n", "* Análise focada nos diferentes canais de venda disponíveis no dataset.\n", "\n", "## 2. Construção da Métrica de Análise\n", "\n", "* Definição de **Ticket <PERSON>** como:\n", "\n", "$$\n", "Ticket\\_Medio = \\frac{Valor\\_Total}{Quantidade}\n", "$$\n", "\n", "* Agregação dos dados por `Dim_Lojas.CANAL_VENDA` (canal de venda) e `Regiao` (região geográfica).\n", "* Cálculo da média do ticket médio em cada combinação, resultando em uma matriz (`ticket_medio`) com:\n", "\n", "  * Linhas = Canais de Venda.\n", "  * Colunas = Regiões do Brasil.\n", "  * Valores = Média do ticket médio.\n", "\n", "## 3. Visual<PERSON><PERSON><PERSON><PERSON>\n", "\n", "Foram gerados dois gráficos complementares a partir da matriz `ticket_medio`:\n", "\n", "* **Gráfico de Barras:**\n", "\n", "  * Eixo X: <PERSON><PERSON> de Venda.\n", "  * Eixo Y: Ticket <PERSON> (R\\$).\n", "  * Barras coloridas representam as regiões.\n", "  * Permite comparar, em valores absolutos, como cada canal de venda performa em diferentes regiões.\n", "\n", "* **Heatmap (Mapa de Calor):**\n", "\n", "  * Matriz colorida com os mesmos dados agregados.\n", "  * Uso de escala contínua personalizada para destacar contrastes.\n", "  * Anotações exibem os valores numéricos de ticket médio em cada célula.\n", "  * Facilita a leitura de padrões e comparações rápidas entre regiões e canais de venda.\n", "\n", "## 4. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "* **Top 5 Combinações:** Ranking das 5 melhores combinações de canal de venda e região por ticket médio.\n", "* **Análise por Canal:** Média geral do ticket médio para cada canal de venda.\n", "* **<PERSON><PERSON><PERSON><PERSON>or Regi<PERSON>:** Média geral do ticket médio para cada região.\n", "\n", "## 5. <PERSON><PERSON>ques Iniciais (Insights Exploratórios)\n", "\n", "* O ticket médio varia significativamente entre regiões, sugerindo diferenças de poder de compra ou perfil de consumo.\n", "* Diferentes canais de venda apresentam performances distintas em cada região.\n", "* A análise permite identificar os canais mais rentáveis por região geográfica.\n", "* O uso conjunto do gráfico de barras e do heatmap permite tanto comparar magnitudes absolutas (barras) quanto identificar padrões visuais e contrastes (heatmap)."]}, {"cell_type": "code", "execution_count": 9, "id": "d87a1e65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 10, "id": "e9258055", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Canais de venda únicos após processamento: ['ECO CHILLI', 'OTICA', 'VERMELHA']\n", "Regiões disponíveis: ['CENTRO-OESTE', 'NORDESTE', 'NORTE', 'SUDESTE', 'SUL']\n", "\n", "<PERSON><PERSON><PERSON> da matriz ticket_medio: (3, 5)\n"]}], "source": ["# Criando a nova coluna de Regiões\n", "df['Regiao'] = df['Dim_Lojas.REGIAO_CHILLI']\n", "\n", "# Filtrar apenas os tipos de PDV desejados\n", "df_oticas = df\n", "\n", "# Criar a coluna de Ticket Médio (Valor_Total dividido pela Quantidade)\n", "df_oticas[\"Ticket_Medio\"] = df_oticas[\"Valor_Total\"] / df_oticas[\"Quantidade\"]\n", "\n", "# Agrupar por Canal de Venda (CANAL_VENDA) e Região, calcular média do ticket médio\n", "ticket_medio = (\n", "    df_oticas.groupby([\"Dim_Lojas.CANAL_VENDA\", \"Regiao\"])[\"Ticket_Medio\"]\n", "    .mean()\n", "    .unstack()\n", "    .fillna(0)  # <PERSON><PERSON><PERSON> valores NaN com 0\n", ")\n", "\n", "# Configurações de estilo para os gráficos\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "plt.rcParams['figure.facecolor'] = 'white'\n", "plt.rcParams['axes.facecolor'] = 'white'\n", "\n", "# Plotar gráfico de barras com estilo melhorado\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# Cores personalizadas para as regiões\n", "cores_regioes = {\n", "    'CENTRO-OESTE': '#FF6B6B',\n", "    'NORDESTE': '#4ECDC4', \n", "    'NORTE': '#45B7D1',\n", "    'SUDESTE': '#96CEB4',\n", "    'SUL': '#FFEAA7'\n", "}\n", "\n", "# Plotar com cores personalizadas\n", "ticket_medio.plot(kind=\"bar\", ax=ax, color=[cores_regioes.get(col, '#95A5A6') for col in ticket_medio.columns])\n", "\n", "# Melhorias no estilo\n", "ax.set_title(\"Ticket Médio por Canal de Venda e Região\", fontsize=18, fontweight='bold', pad=20)\n", "ax.set_ylabel(\"Ticket Médio (R$)\", fontsize=14, fontweight='bold')\n", "ax.set_xlabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Configurar a legenda\n", "ax.legend(title=\"<PERSON><PERSON><PERSON>\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", \n", "          title_fontsize=12, fontsize=11, frameon=True, fancybox=True, shadow=True)\n", "\n", "# Rotacionar labels do eixo x para melhor legibilidade\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=11)\n", "\n", "# Adicionar grid mais sutil\n", "ax.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# Formatar valores no eixo y como moeda brasileira\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))\n", "\n", "# Adicionar valores nas barras\n", "for container in ax.containers:\n", "    ax.bar_label(container, fmt='R$ %.0f', rotation=90, fontsize=9, padding=3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Exibir informações sobre os dados processados\n", "print(f\"Canais de venda únicos após processamento: {sorted(df_oticas['Dim_Lojas.CANAL_VENDA'].unique())}\")\n", "print(f\"Regiões disponíveis: {sorted(df_oticas['Regiao'].unique())}\")\n", "print(f\"\\nShape da matriz ticket_medio: {ticket_medio.shape}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "c278336f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TOP 5 TICKET MÉDIO POR CANAL DE VENDA E REGIÃO\n", "1º lugar: ECO CHILLI - NORTE: R$ 417\n", "2º lugar: ECO CHILLI - NORDESTE: R$ 375\n", "3º lugar: OTICA - SUDESTE: R$ 339\n", "4º lugar: OTICA - NORDESTE: R$ 320\n", "5º lugar: OTICA - CENTRO-OESTE: R$ 319\n", "\n", "TICKET MÉDIO POR CANAL DE VENDA (MÉDIA GERAL)\n", "OTICA: R$ 321\n", "VERMELHA: R$ 269\n", "ECO CHILLI: R$ 263\n", "\n", "TICKET MÉDIO POR REGIÃO (MÉDIA GERAL)\n", "NORDESTE: R$ 282\n", "SUL: R$ 281\n", "SUDESTE: R$ 281\n", "CENTRO-OESTE: R$ 276\n", "NORTE: R$ 260\n"]}], "source": ["# --- <PERSON><PERSON><PERSON> o Top 5 geral com melhor formatação ---\n", "top5 = (\n", "    ticket_medio.reset_index()  # tira MultiIndex (Canal e Região viram colunas)\n", "    .melt(id_vars=\"Dim_Lojas.CANAL_VENDA\", var_name=\"Regiao\", value_name=\"Ticket_Medio\")  # formato longo\n", "    .dropna(subset=[\"Ticket_Medio\"])  # remove NaN\n", "    .query(\"Ticket_Medio > 0\")  # remove valores zero\n", "    .sort_values(\"Ticket_Medio\", ascending=False)  # ordena do maior pro menor\n", "    .head(5)  # pega os 5 primeiros\n", "    .reset_index(drop=True)\n", ")\n", "\n", "print(\"TOP 5 TICKET MÉDIO POR CANAL DE VENDA E REGIÃO\")\n", "for i, row in top5.iterrows():\n", "    posicao = i + 1\n", "    canal = row['Dim_Lojas.CANAL_VENDA']\n", "    regiao = row['Regiao']\n", "    valor = row['Ticket_Medio']\n", "    print(f\"{posicao}º lugar: {canal} - {regiao}: R$ {valor:,.0f}\")\n", "\n", "# Análise adicional por canal\n", "print(\"\\nTICKET MÉDIO POR CANAL DE VENDA (MÉDIA GERAL)\")\n", "ticket_por_canal = df_oticas.groupby('Dim_Lojas.CANAL_VENDA')['Ticket_Medio'].mean().sort_values(ascending=False)\n", "for canal, valor in ticket_por_canal.items():\n", "    print(f\"{canal}: R$ {valor:,.0f}\")\n", "\n", "# Análise adicional por região\n", "print(\"\\nTICKET MÉDIO POR REGIÃO (MÉDIA GERAL)\")\n", "ticket_por_regiao = df_oticas.groupby('Regiao')['Ticket_Medio'].mean().sort_values(ascending=False)\n", "for regiao, valor in ticket_por_regiao.items():\n", "    print(f\"{regiao}: R$ {valor:,.0f}\")"]}, {"cell_type": "code", "execution_count": 12, "id": "39c3b052", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib.colors import LinearSegmentedColormap\n", "\n", "# Criar colormap personalizado mais adequado para ticket médio (azul → verde → amarelo → vermelho)\n", "cores_personalizadas = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']\n", "cmap = LinearSegmentedColormap.from_list(\"ticket_medio\", cores_personalizadas, N=256)\n", "\n", "# Configurar o estilo\n", "plt.style.use('seaborn-v0_8-white')\n", "\n", "# Plotar gráfico de heatmap melhorado\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# Criar o heatmap com melhorias visuais\n", "sns.heatmap(ticket_medio, \n", "            annot=True, \n", "            fmt='.0f',  # Formato sem casas decimais para valores em reais\n", "            cmap=cmap, \n", "            cbar_kws={\n", "                'label': 'Ticket <PERSON> (R$)',\n", "                'shrink': 0.8,\n", "                'aspect': 20\n", "            },\n", "            linewidths=1,\n", "            linecolor='white',\n", "            square=False,\n", "            annot_kws={\n", "                'fontsize': 11,\n", "                'fontweight': 'bold'\n", "            },\n", "            ax=ax)\n", "\n", "# Melhorias no título e labels\n", "ax.set_title(\"Heatmap: Ticket Médio por Canal de Venda e Região\", \n", "             fontsize=18, fontweight='bold', pad=25)\n", "ax.set_xlabel(\"Região\", fontsize=14, fontweight='bold')\n", "ax.set_ylabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Melhorar a formatação dos labels\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=12)\n", "plt.setp(ax.get_yticklabels(), rotation=0, fontsize=12)\n", "\n", "# Adicionar borda ao redor do heatmap\n", "for spine in ax.spines.values():\n", "    spine.set_visible(True)\n", "    spine.set_linewidth(2)\n", "    spine.set_edgecolor('black')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "cd881543", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MATRIZ DE VOLUME DE VENDAS (QUANTIDADE):\n", "Regiao                 CENTRO-OESTE  NORDESTE   NORTE  SUDESTE     SUL\n", "Dim_Lojas.CANAL_VENDA                                                 \n", "ECO CHILLI                      0.0       4.0     4.0    298.0   119.0\n", "OTICA                         411.0     394.0   438.0   2089.0   795.0\n", "VERMELHA                     1870.0    2338.0  1210.0   7423.0  2631.0\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "TOP 5 VOLUME DE VENDAS POR CANAL DE VENDA E REGIÃO\n", "1º lugar: VERMELHA - SUDESTE: 7,423 unidades\n", "2º lugar: VERMELHA - SUL: 2,631 unidades\n", "3º lugar: VERMELHA - NORDESTE: 2,338 unidades\n", "4º lugar: OTICA - SUDESTE: 2,089 unidades\n", "5º lugar: VERMELHA - CENTRO-OESTE: 1,870 unidades\n", "\n", "VOLUME DE VENDAS POR CANAL DE VENDA (TOTAL GERAL)\n", "VERMELHA: 15,472 unidades\n", "OTICA: 4,127 unidades\n", "ECO CHILLI: 425 unidades\n", "\n", "VOLUME DE VENDAS POR REGIÃO (TOTAL GERAL)\n", "SUDESTE: 9,810 unidades\n", "SUL: 3,545 unidades\n", "NORDESTE: 2,736 unidades\n", "CENTRO-OESTE: 2,281 unidades\n", "NORTE: 1,652 unidades\n"]}], "source": ["# Análise de Volume de Vendas por Canal de Venda e Região\n", "\n", "# Criar a matriz de volume de vendas (quantidade total vendida)\n", "volume_vendas = (\n", "    df_oticas.groupby([\"Dim_Lojas.CANAL_VENDA\", \"Regiao\"])[\"Quantidade\"]\n", "    .sum()\n", "    .unstack()\n", "    .fillna(0)  # <PERSON><PERSON><PERSON> valores NaN com 0\n", ")\n", "\n", "print(\"MATRIZ DE VOLUME DE VENDAS (QUANTIDADE):\")\n", "print(volume_vendas)\n", "print()\n", "\n", "# Configurações de estilo para os gráficos\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "plt.rcParams['figure.facecolor'] = 'white'\n", "plt.rcParams['axes.facecolor'] = 'white'\n", "\n", "# Plotar gráfico de barras para volume de vendas\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# Cores personalizadas para as regiões (mesmas cores do gráfico anterior)\n", "cores_regioes = {\n", "    'CENTRO-OESTE': '#FF6B6B',\n", "    'NORDESTE': '#4ECDC4', \n", "    'NORTE': '#45B7D1',\n", "    'SUDESTE': '#96CEB4',\n", "    'SUL': '#FFEAA7'\n", "}\n", "\n", "# Plotar com cores personalizadas\n", "volume_vendas.plot(kind=\"bar\", ax=ax, color=[cores_regioes.get(col, '#95A5A6') for col in volume_vendas.columns])\n", "\n", "# Melhorias no estilo\n", "ax.set_title(\"Volume de Vendas por Canal de Venda e Região\", fontsize=18, fontweight='bold', pad=20)\n", "ax.set_ylabel(\"Quantidade Vendida (unidades)\", fontsize=14, fontweight='bold')\n", "ax.set_xlabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Configurar a legenda\n", "ax.legend(title=\"<PERSON><PERSON><PERSON>\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", \n", "          title_fontsize=12, fontsize=11, frameon=True, fancybox=True, shadow=True)\n", "\n", "# Rotacionar labels do eixo x para melhor legibilidade\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=11)\n", "\n", "# Adicionar grid mais sutil\n", "ax.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# Formatar valores no eixo y com separador de milhares\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))\n", "\n", "# Adicionar valores nas barras\n", "for container in ax.containers:\n", "    ax.bar_label(container, fmt='%.0f', rotation=90, fontsize=9, padding=3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# --- TOP 5 VOLUME DE VENDAS ---\n", "top5_volume = (\n", "    volume_vendas.reset_index()  # tira MultiIndex\n", "    .melt(id_vars=\"Dim_Lojas.CANAL_VENDA\", var_name=\"Regiao\", value_name=\"Volume_Vendas\")  # formato longo\n", "    .dropna(subset=[\"Volume_Vendas\"])  # remove NaN\n", "    .query(\"Volume_Vendas > 0\")  # remove valores zero\n", "    .sort_values(\"Volume_Vendas\", ascending=False)  # ordena do maior pro menor\n", "    .head(5)  # pega os 5 primeiros\n", "    .reset_index(drop=True)\n", ")\n", "\n", "print(\"\\nTOP 5 VOLUME DE VENDAS POR CANAL DE VENDA E REGIÃO\")\n", "for i, row in top5_volume.iterrows():\n", "    posicao = i + 1\n", "    canal = row['Dim_Lojas.CANAL_VENDA']\n", "    regiao = row['Regiao']\n", "    volume = row['Volume_Vendas']\n", "    print(f\"{posicao}º lugar: {canal} - {regiao}: {volume:,.0f} unidades\")\n", "\n", "# Análise adicional por canal\n", "print(\"\\nVOLUME DE VENDAS POR CANAL DE VENDA (TOTAL GERAL)\")\n", "volume_por_canal = df_oticas.groupby('Dim_Lojas.CANAL_VENDA')['Quantidade'].sum().sort_values(ascending=False)\n", "for canal, volume in volume_por_canal.items():\n", "    print(f\"{canal}: {volume:,.0f} unidades\")\n", "\n", "# Análise adicional por região\n", "print(\"\\nVOLUME DE VENDAS POR REGIÃO (TOTAL GERAL)\")\n", "volume_por_regiao = df_oticas.groupby('Regiao')['Quantidade'].sum().sort_values(ascending=False)\n", "for regiao, volume in volume_por_regiao.items():\n", "    print(f\"{regiao}: {volume:,.0f} unidades\")"]}, {"cell_type": "code", "execution_count": 14, "id": "3c887fd2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "COMPARAÇÃO: CORRELAÇÃO ENTRE TICKET MÉDIO E VOLUME DE VENDAS\n", "======================================================================\n", "Correlação entre Ticket Médio e Volume de Vendas por Canal:\n", "ECO CHILLI: -0.889\n", "OTICA: 0.538\n", "VERMELHA: 0.083\n"]}], "source": ["# Heatmap do Volume de Vendas por Canal de Venda e Região\n", "\n", "# Criar colormap personalizado para volume (azul → verde → amarelo → laranja)\n", "cores_volume = ['#0D47A1', '#1976D2', '#42A5F5', '#81C784', '#FDD835', '#FF9800']\n", "cmap_volume = LinearSegmentedColormap.from_list(\"volume_vendas\", cores_volume, N=256)\n", "\n", "# Configurar o estilo\n", "plt.style.use('seaborn-v0_8-white')\n", "\n", "# Plotar gráfico de heatmap para volume de vendas\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# Criar o heatmap com melhorias visuais\n", "sns.heatmap(volume_vendas, \n", "            annot=True, \n", "            fmt='.0f',  # Formato sem casas decimais para unidades\n", "            cmap=cmap_volume, \n", "            cbar_kws={\n", "                'label': 'Volume de Vendas (unidades)',\n", "                'shrink': 0.8,\n", "                'aspect': 20\n", "            },\n", "            linewidths=1,\n", "            linecolor='white',\n", "            square=False,\n", "            annot_kws={\n", "                'fontsize': 11,\n", "                'fontweight': 'bold'\n", "            },\n", "            ax=ax)\n", "\n", "# Melhorias no título e labels\n", "ax.set_title(\"Heatmap: Volume de Vendas por Canal de Venda e Região\", \n", "             fontsize=18, fontweight='bold', pad=25)\n", "ax.set_xlabel(\"Região\", fontsize=14, fontweight='bold')\n", "ax.set_ylabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Melhorar a formatação dos labels\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=12)\n", "plt.setp(ax.get_yticklabels(), rotation=0, fontsize=12)\n", "\n", "# Adicionar borda ao redor do heatmap\n", "for spine in ax.spines.values():\n", "    spine.set_visible(True)\n", "    spine.set_linewidth(2)\n", "    spine.set_edgecolor('black')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Comparação entre Ticket Médio e Volume de Vendas\n", "print(\"\\nCOMPARAÇÃO: CORRELAÇÃO ENTRE TICKET MÉDIO E VOLUME DE VENDAS\")\n", "print(\"=\"*70)\n", "\n", "# Calcular correlação entre ticket médio e volume por canal\n", "correlacao_por_canal = []\n", "for canal in volume_vendas.index:\n", "    ticket_canal = ticket_medio.loc[canal]\n", "    volume_canal = volume_vendas.loc[canal]\n", "    \n", "    # Criar arrays apenas com valores válidos (não zero/NaN)\n", "    mask = (ticket_canal > 0) & (volume_canal > 0)\n", "    if mask.sum() > 1:  # Precisamos de pelo menos 2 pontos para correlação\n", "        corr = np.corrcoef(ticket_canal[mask], volume_canal[mask])[0, 1]\n", "        correlacao_por_canal.append((canal, corr))\n", "\n", "print(\"Correlação entre Ticket Médio e Volume de Vendas por Canal:\")\n", "for canal, corr in correlacao_por_canal:\n", "    if not np.isnan(corr):\n", "        print(f\"{canal}: {corr:.3f}\")\n", "    else:\n", "        print(f\"{canal}: Dados insuficientes para correlação\")"]}, {"cell_type": "code", "execution_count": 15, "id": "b4bf74a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======================================================================\n", "TESTANDO FILTRO DE VOLUME MÍNIMO DE 100 UNIDADES POR CANAL-REGIÃO\n", "======================================================================\n", "Dados iniciais: 40,291 registros\n", "Remoção DEVOLUCAO DE MERCADORIA: 1,281 registros removidos\n", "Preço Varejo > 1: 467 registros removidos\n", "Total Preço Varejo > 1: 0 registros removidos\n", "Validação cálculo valor total: 0 registros removidos\n", "Validação valor total (tolerância 2): 0 registros removidos\n", "Idade entre 10 e 100 anos: 19,255 registros removidos\n", "Idade >= 18 anos no cadastro: 594 registros removidos\n", "Remoção duplicatas: 96 registros removidos\n", "Lojas com Tipo PDV válido: 231 registros removidos\n", "Produtos com nome válido: 0 registros removidos\n", "Produtos com grupo válido: 0 registros removidos\n", "Limpeza de espaços em branco nas colunas categóricas concluída\n", "Regras de negócios aplicadas\n", "\n", "Filtro de volume mínimo (100 unidades):\n", "Combinações Canal-Região com volume suficiente:\n", "  ECO CHILLI - SUDESTE: 298 unidades\n", "  ECO CHILLI - SUL: 119 unidades\n", "  OTICA - CENTRO-OESTE: 411 unidades\n", "  OTICA - NORDESTE: 394 unidades\n", "  OTICA - NORTE: 438 unidades\n", "  OTICA - SUDESTE: 2,089 unidades\n", "  OTICA - SUL: 795 unidades\n", "  VERMELHA - CENTRO-OESTE: 1,870 unidades\n", "  VERMELHA - NORDESTE: 2,338 unidades\n", "  VERMELHA - NORTE: 1,210 unidades\n", "  VERMELHA - SUDESTE: 7,423 unidades\n", "  VERMELHA - SUL: 2,631 unidades\n", "\n", "Combinações Canal-Região removidas (volume insuficiente):\n", "  ECO CHILLI - NORDESTE: 4 unidades\n", "  ECO CHILLI - NORTE: 4 unidades\n", "Total de registros removidos pelo filtro de volume: 8\n", "\n", "Dados finais: 18,359 registros\n", "\n", "Comparação de resultados:\n", "Dados sem filtro de volume: 18,367 registros\n", "Dados com filtro de volume ≥ 100: 18,359 registros\n", "Registros removidos: 8\n", "Percentual removido: 0.04%\n"]}], "source": ["# Testando o Filtro de Volume Mínimo\n", "\n", "# Recarregar o módulo para capturar as mud<PERSON><PERSON>s\n", "%autoreload 2\n", "\n", "print(\"=\"*70)\n", "print(\"TESTANDO FILTRO DE VOLUME MÍNIMO DE 100 UNIDADES POR CANAL-REGIÃO\")\n", "print(\"=\"*70)\n", "\n", "# Carregar dados com filtro de volume mínimo\n", "from data_filtering import apply_business_filters_with_min_volume\n", "\n", "df_com_filtro = apply_business_filters_with_min_volume('../assets/dados.csv', min_volume=100, verbose=True)\n", "\n", "print(f\"\\nComparação de resultados:\")\n", "print(f\"Dados sem filtro de volume: {len(df):,} registros\")\n", "print(f\"Dados com filtro de volume ≥ 100: {len(df_com_filtro):,} registros\")\n", "print(f\"Registros removidos: {len(df) - len(df_com_filtro):,}\")\n", "print(f\"Percentual removido: {((len(df) - len(df_com_filtro)) / len(df) * 100):.2f}%\")"]}, {"cell_type": "code", "execution_count": 16, "id": "04e15a5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======================================================================\n", "ANÁLISE COM FILTRO DE VOLUME MÍNIMO APLICADO\n", "======================================================================\n", "VOLUME DE VENDAS APÓS FILTRO:\n", "Regiao                 CENTRO-OESTE  NORDESTE   NORTE  SUDESTE     SUL\n", "Dim_Lojas.CANAL_VENDA                                                 \n", "ECO CHILLI                      0.0       0.0     0.0    298.0   119.0\n", "OTICA                         411.0     394.0   438.0   2089.0   795.0\n", "VERMELHA                     1870.0    2338.0  1210.0   7423.0  2631.0\n", "\n", "TICKET MÉDIO APÓS FILTRO:\n", "Regiao                 CENTRO-OESTE    NORDESTE       NORTE     SUDESTE  \\\n", "Dim_Lojas.CANAL_VENDA                                                     \n", "ECO CHILLI                 0.000000    0.000000    0.000000  253.798745   \n", "OTICA                    319.482379  320.059708  256.275673  338.747978   \n", "VERMELHA                 266.942098  276.300497  260.973934  267.796398   \n", "\n", "Regiao                        SUL  \n", "Dim_Lojas.CANAL_VENDA              \n", "ECO CHILLI             278.052594  \n", "OTICA                  312.096157  \n", "VERMELHA               273.301872  \n", "\n"]}, {"data": {"image/png": "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********************************************************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", "text/plain": ["<Figure size 2000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["COMPARAÇÃO ANTES vs DEPOIS DO FILTRO:\n", "==================================================\n", "CANAIS DE VENDA:\n", "Antes do filtro: ['ECO CHILLI', 'OTICA', 'VERMELHA']\n", "Depois do filtro: ['ECO CHILLI', 'OTICA', 'VERMELHA']\n", "\n", "REGIÕES:\n", "Antes do filtro: ['CENTRO-OESTE', 'NORDESTE', 'NORTE', 'SUDESTE', 'SUL']\n", "Depois do filtro: ['CENTRO-OESTE', 'NORDESTE', 'NORTE', 'SUDESTE', 'SUL']\n", "\n", "COMBINAÇÕES CANAL-REGIÃO:\n", "Antes do filtro: 14 combinações\n", "Depois do filtro: 12 combinações\n", "Combinações removidas: 2\n"]}], "source": ["# Gráficos com Dados Filtrados (Volume Mínimo ≥ 100)\n", "\n", "print(\"=\"*70)\n", "print(\"ANÁLISE COM FILTRO DE VOLUME MÍNIMO APLICADO\")\n", "print(\"=\"*70)\n", "\n", "# Preparar dados filtrados\n", "df_com_filtro['Regiao'] = df_com_filtro['Dim_Lojas.REGIAO_CHILLI']\n", "df_com_filtro[\"Ticket_Medio\"] = df_com_filtro[\"Valor_Total\"] / df_com_filtro[\"Quantidade\"]\n", "\n", "# Calcular volume de vendas com dados filtrados\n", "volume_vendas_filtrado = (\n", "    df_com_filtro.groupby([\"Dim_Lojas.CANAL_VENDA\", \"Regiao\"])[\"Quantidade\"]\n", "    .sum()\n", "    .unstack()\n", "    .fillna(0)\n", ")\n", "\n", "# Calcular ticket médio com dados filtrados\n", "ticket_medio_filtrado = (\n", "    df_com_filtro.groupby([\"Dim_Lojas.CANAL_VENDA\", \"Regiao\"])[\"Ticket_Medio\"]\n", "    .mean()\n", "    .unstack()\n", "    .fillna(0)\n", ")\n", "\n", "print(\"VOLUME DE VENDAS APÓS FILTRO:\")\n", "print(volume_vendas_filtrado)\n", "print()\n", "\n", "print(\"TICKET MÉDIO APÓS FILTRO:\")\n", "print(ticket_medio_filtrado)\n", "print()\n", "\n", "# Plotar gráfico de barras do volume filtrado\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))\n", "\n", "# Volume de vendas filtrado\n", "volume_vendas_filtrado.plot(kind=\"bar\", ax=ax1, color=[cores_regioes.get(col, '#95A5A6') for col in volume_vendas_filtrado.columns])\n", "ax1.set_title(\"Volume de Vendas (≥100 por Canal-Região)\", fontsize=16, fontweight='bold', pad=20)\n", "ax1.set_ylabel(\"Quantidade Vendida (unidades)\", fontsize=12, fontweight='bold')\n", "ax1.set_xlabel(\"Canal de Venda\", fontsize=12, fontweight='bold')\n", "ax1.legend(title=\"<PERSON><PERSON><PERSON>\", bbox_to_anchor=(1.05, 1), loc=\"upper left\")\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right', fontsize=10)\n", "ax1.grid(True, alpha=0.3, linestyle='--')\n", "ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))\n", "\n", "# Ticket médio filtrado\n", "ticket_medio_filtrado.plot(kind=\"bar\", ax=ax2, color=[cores_regioes.get(col, '#95A5A6') for col in ticket_medio_filtrado.columns])\n", "ax2.set_title(\"Ticket Médio (≥100 por Canal-Região)\", fontsize=16, fontweight='bold', pad=20)\n", "ax2.set_ylabel(\"Ticket Médio (R$)\", fontsize=12, fontweight='bold')\n", "ax2.set_xlabel(\"Canal de Venda\", fontsize=12, fontweight='bold')\n", "ax2.legend(title=\"<PERSON><PERSON><PERSON>\", bbox_to_anchor=(1.05, 1), loc=\"upper left\")\n", "plt.setp(ax2.get_xticklabels(), rotation=45, ha='right', fontsize=10)\n", "ax2.grid(True, alpha=0.3, linestyle='--')\n", "ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Comparação de estatísticas\n", "print(\"COMPARAÇÃO ANTES vs DEPOIS DO FILTRO:\")\n", "print(\"=\"*50)\n", "\n", "print(\"CANAIS DE VENDA:\")\n", "print(\"Antes do filtro:\", sorted(df['Dim_Lojas.CANAL_VENDA'].unique()))\n", "print(\"Depois do filtro:\", sorted(df_com_filtro['Dim_Lojas.CANAL_VENDA'].unique()))\n", "\n", "print(\"\\nREGIÕES:\")\n", "print(\"Antes do filtro:\", sorted(df['Dim_Lojas.REGIAO_CHILLI'].unique()))\n", "print(\"Depois do filtro:\", sorted(df_com_filtro['Dim_Lojas.REGIAO_CHILLI'].unique()))\n", "\n", "print(f\"\\nCOMBINAÇÕES CANAL-REGIÃO:\")\n", "combinacoes_antes = len(df.groupby(['Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI']).size())\n", "combinacoes_depois = len(df_com_filtro.groupby(['Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI']).size())\n", "print(f\"Antes do filtro: {combinacoes_antes} combinações\")\n", "print(f\"Depois do filtro: {combinacoes_depois} combinações\")\n", "print(f\"Combinações removidas: {combinacoes_antes - combinacoes_depois}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}