import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

# Adicionar o diretório pai ao path para importar o módulo de filtragem
sys.path.append(os.path.dirname(os.path.abspath('.')))

# Importar o módulo de filtragem
from data_filtering import apply_business_filters

# Carregar e filtrar os dados usando o módulo reutilizável

df = apply_business_filters('../assets/dados.csv')

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np


df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']
df = df[df['Preco_Varejo'] > 1]
df = df[df['Total_Preco_Varejo'] > 1]

df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']
df = df[df['diferenca'] < 1]

df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')
df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')
df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')

df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]

df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25
df = df[df['idade_cadastro'] >= 18]

df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])

df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']
df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]

df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]
df = df[df['Dim_Produtos.Nome'].notnull()]
df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]

df['Dim_Lojas.Tipo_PDV'] = df['Dim_Lojas.Tipo_PDV'].astype(str).str.strip()
df['Dim_Produtos.Sub_Grupo'] = df['Dim_Produtos.Sub_Grupo'].astype(str).str.strip()

def estatisticas_numericas(coluna, preencher_na=False):
    """
    Gera estatísticas descritivas para uma coluna numérica do dataframe global df.

    Parâmetros:
    - coluna: nome da coluna no dataframe df.
    - preencher_na: se True, preenche valores nulos com a média da coluna.

    Retorna:
    - dict com média, mediana, moda, desvio padrão, contagem de valores, soma, mínimo e máximo.
    """
    if coluna not in df.columns:
        raise ValueError(f"A coluna '{coluna}' não existe no dataframe.")
    
    serie = df[coluna]

    if not pd.api.types.is_numeric_dtype(serie):
        raise TypeError(f"A coluna '{coluna}' não é numérica.")

    media = float(serie.mean())
    mediana = float(serie.median())
    moda = float(serie.mode().iloc[0]) if not serie.mode().empty else None
    desvio_padrao = float(serie.std())
    contagem_valores = float(serie.count())
    soma_total = float(serie.sum())
    minimo = float(serie.min())
    maximo = float(serie.max())

    if preencher_na:
        df[coluna].fillna(media, inplace=True)

    return {
        "coluna": coluna,
        "media": media,
        "mediana": mediana,
        "moda": moda,
        "desvio_padrao": desvio_padrao,
        "soma_total": soma_total,
        "minimo": minimo,
        "maximo": maximo,
        "contagem_valores": contagem_valores
    }

import json

colunas_analise = ['Preco_Custo', 'DESCONTO_CALCULADO', 'Quantidade', 'Valor_Total']

# Exemplo de uso da função
# Substitua 'nome_da_coluna' pelo nome de uma coluna numérica do seu dataframe
for coluna in colunas_analise:
    resultado = estatisticas_numericas(coluna)
    print(f"Análise da coluna {coluna}:")
    print(json.dumps(resultado, ensure_ascii=False, indent=4))
    print()